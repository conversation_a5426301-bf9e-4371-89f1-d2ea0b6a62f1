import { Room } from '@/types'

/**
 * Transform database room data to match TypeScript interface
 * Converts snake_case fields to camelCase
 */
export const transformRoomData = (dbRoom: any): Room => {
  return {
    ...dbRoom,
    internalName: dbRoom.internal_name,
    priceCurrency: dbRoom.price_currency || 'SAR',
    priceUsd: dbRoom.price_usd,
    reviewCount: dbRoom.review_count || 0,
    roomType: dbRoom.room_type || 'standard',
    distanceToHaram: dbRoom.distance_to_haram || 500,
    bedType: dbRoom.bed_type || 'Double',
    bedCount: dbRoom.bed_count || 1,
    maxGuests: dbRoom.max_guests || 2,
    bathroomCount: dbRoom.bathroom_count || 1,
    hasBalcony: dbRoom.has_balcony || false,
    hasKitchen: dbRoom.has_kitchen || false,
    hasBathroom: dbRoom.has_bathroom !== false,
    mainImageIndex: dbRoom.main_image_index || 0,
    // Simple location field
    googleMapsLink: dbRoom.google_maps_link,
  };
};

/**
 * Get room images with main image first
 */
export const getRoomImages = (room: Room): string[] => {
  // If room has uploaded images, reorder based on main image index
  if (room.images && room.images.length > 0) {
    const images = [...room.images];
    const mainIndex = (room as any).main_image_index || 0;
    
    // Move main image to front if specified
    if (mainIndex > 0 && mainIndex < images.length) {
      const mainImage = images[mainIndex];
      images.splice(mainIndex, 1);
      images.unshift(mainImage);
    }
    
    return images;
  }
  
  // Otherwise, use main image plus some fallbacks
  return [
    room.image,
    'https://images.unsplash.com/photo-1566665797739-1674de7a421a?q=80&w=2074&auto=format&fit=crop',
    'https://images.unsplash.com/photo-1631049552240-59c37f38802b?q=80&w=2070&auto=format&fit=crop',
    'https://images.unsplash.com/photo-1595576508898-0ad5c879a061?q=80&w=2074&auto=format&fit=crop',
  ].filter(Boolean); // Remove any null/undefined values
};

/**
 * Get the main/primary image for a room
 */
export const getMainRoomImage = (room: Room): string => {
  const images = getRoomImages(room);
  return images[0] || room.image;
};

/**
 * Format price with currency symbol
 */
export const formatPrice = (price: number, currency: string = 'SAR'): string => {
  const symbols = {
    SAR: '﷼',
    USD: '$',
    EUR: '€',
    GBP: '£'
  };
  
  const symbol = symbols[currency as keyof typeof symbols] || currency;
  return `${symbol}${price}`;
};

/**
 * Calculate booking totals
 */
export const calculateBookingTotals = (price: number, nights: number) => {
  const subtotal = price * nights;
  const serviceFee = Math.round(subtotal * 0.14); // 14% service fee
  const taxes = Math.round(subtotal * 0.12); // 12% taxes
  const total = subtotal + serviceFee + taxes;
  
  return {
    subtotal,
    serviceFee,
    taxes,
    total,
    nights
  };
}; 