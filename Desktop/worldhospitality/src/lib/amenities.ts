// Hotel Amenities Master List
export const HOTEL_AMENITIES = [
  // Essential Amenities
  { id: 'free-wifi', label: 'Free WiFi', category: 'essential' },
  { id: 'air-conditioning', label: 'Air Conditioning', category: 'essential' },
  { id: 'prayer-mat', label: 'Prayer Mat', category: 'essential' },
  { id: 'qibla-direction', label: 'Qibla Direction', category: 'essential' },
  { id: 'daily-cleaning', label: 'Daily Cleaning', category: 'essential' },

  // Room Features
  { id: 'balcony', label: 'Balcony', category: 'room' },
  { id: 'city-view', label: 'City View', category: 'room' },
  { id: 'haram-view', label: 'Haram View', category: 'room' },
  { id: 'sea-view', label: 'Sea View', category: 'room' },
  { id: 'mountain-view', label: 'Mountain View', category: 'room' },
  { id: 'soundproof', label: 'Soundproof Windows', category: 'room' },
  { id: 'walk-in-shower', label: 'Walk-in Shower', category: 'room' },
  { id: 'bathtub', label: 'Bathtub', category: 'room' },

  // Kitchen & Dining
  { id: 'kitchenette', label: 'Kitchenette', category: 'kitchen' },
  { id: 'full-kitchen', label: 'Full Kitchen', category: 'kitchen' },
  { id: 'mini-fridge', label: 'Mini Fridge', category: 'kitchen' },
  { id: 'microwave', label: 'Microwave', category: 'kitchen' },
  { id: 'coffee-maker', label: 'Coffee Maker', category: 'kitchen' },
  { id: 'dining-area', label: 'Dining Area', category: 'kitchen' },
  { id: 'mini-bar', label: 'Mini Bar', category: 'kitchen' },

  // Entertainment & Technology
  { id: 'flat-screen-tv', label: 'Flat Screen TV', category: 'entertainment' },
  { id: 'satellite-channels', label: 'Satellite Channels', category: 'entertainment' },
  { id: 'netflix', label: 'Netflix', category: 'entertainment' },
  { id: 'bluetooth-speaker', label: 'Bluetooth Speaker', category: 'entertainment' },
  { id: 'usb-charging', label: 'USB Charging Ports', category: 'entertainment' },
  { id: 'work-desk', label: 'Work Desk', category: 'entertainment' },

  // Luxury Amenities
  { id: 'room-service', label: 'Room Service', category: 'luxury' },
  { id: 'concierge', label: 'Concierge Service', category: 'luxury' },
  { id: 'vip-treatment', label: 'VIP Treatment', category: 'luxury' },
  { id: 'turndown-service', label: 'Turndown Service', category: 'luxury' },
  { id: 'butler-service', label: 'Butler Service', category: 'luxury' },
  { id: 'spa-access', label: 'Spa Access', category: 'luxury' },

  // Transportation & Location
  { id: 'shuttle-service', label: 'Shuttle Service', category: 'transport' },
  { id: 'direct-haram-access', label: 'Direct Haram Access', category: 'transport' },
  { id: 'parking', label: 'Parking Available', category: 'transport' },
  { id: 'valet-parking', label: 'Valet Parking', category: 'transport' },

  // Family & Accessibility
  { id: 'family-friendly', label: 'Family Friendly', category: 'family' },
  { id: 'kids-area', label: 'Kids Play Area', category: 'family' },
  { id: 'baby-crib', label: 'Baby Crib Available', category: 'family' },
  { id: 'wheelchair-accessible', label: 'Wheelchair Accessible', category: 'family' },
  { id: 'elevator-access', label: 'Elevator Access', category: 'family' },

  // Business & Services
  { id: 'business-center', label: 'Business Center', category: 'business' },
  { id: 'meeting-room', label: 'Meeting Room Access', category: 'business' },
  { id: 'printer-access', label: 'Printer Access', category: 'business' },
  { id: 'laundry-service', label: 'Laundry Service', category: 'business' },
  { id: 'dry-cleaning', label: 'Dry Cleaning', category: 'business' },
  { id: 'luggage-storage', label: 'Luggage Storage', category: 'business' },

  // Safety & Security
  { id: 'safe-deposit', label: 'Safe Deposit Box', category: 'safety' },
  { id: '24h-security', label: '24/7 Security', category: 'safety' },
  { id: 'cctv', label: 'CCTV Monitoring', category: 'safety' },
  { id: 'fire-safety', label: 'Fire Safety System', category: 'safety' },
] as const;

export const AMENITY_CATEGORIES = [
  { id: 'essential', label: 'Essential', color: 'bg-blue-50 text-blue-700' },
  { id: 'room', label: 'Room Features', color: 'bg-green-50 text-green-700' },
  { id: 'kitchen', label: 'Kitchen & Dining', color: 'bg-orange-50 text-orange-700' },
  { id: 'entertainment', label: 'Entertainment', color: 'bg-purple-50 text-purple-700' },
  { id: 'luxury', label: 'Luxury Services', color: 'bg-pink-50 text-pink-700' },
  { id: 'transport', label: 'Transportation', color: 'bg-indigo-50 text-indigo-700' },
  { id: 'family', label: 'Family & Accessibility', color: 'bg-teal-50 text-teal-700' },
  { id: 'business', label: 'Business Services', color: 'bg-gray-50 text-gray-700' },
  { id: 'safety', label: 'Safety & Security', color: 'bg-red-50 text-red-700' },
] as const;

// Currency conversion rates (you can update these or fetch from API)
export const CURRENCY_RATES = {
  SAR: { toUSD: 0.27, symbol: '﷼', name: 'Saudi Riyal' },
  USD: { toUSD: 1, symbol: '$', name: 'US Dollar' },
  EUR: { toUSD: 1.08, symbol: '€', name: 'Euro' },
  GBP: { toUSD: 1.25, symbol: '£', name: 'British Pound' },
} as const;

export const convertPrice = (amount: number, fromCurrency: keyof typeof CURRENCY_RATES, toCurrency: keyof typeof CURRENCY_RATES): number => {
  const usdAmount = amount * CURRENCY_RATES[fromCurrency].toUSD;
  return Math.round(usdAmount / CURRENCY_RATES[toCurrency].toUSD);
}; 