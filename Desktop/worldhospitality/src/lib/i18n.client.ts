import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import enCommon from '../i18n/locales/en/common.json';
import arCommon from '../i18n/locales/ar/common.json';

const resources = {
  en: { common: enCommon },
  ar: { common: arCommon },
};

if (!i18n.isInitialized) {
  i18n
    .use(initReactI18next)
    .init({
      resources,
      lng: 'en',
      fallbackLng: 'en',
      supportedLngs: ['en', 'ar'],
      ns: ['common'],
      defaultNS: 'common',
      interpolation: {
        escapeValue: false,
      },
      react: {
        useSuspense: false,
      },
    });
}

export default i18n; 