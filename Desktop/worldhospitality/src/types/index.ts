export interface User {
  id: number;
  name: string;
  email: string;
  phone: string;
  role: 'guest' | 'admin';
  createdAt: string;
}

export interface Booking {
  id: number;
  userId: number;
  roomId: number;
  checkIn: string;
  checkOut: string;
  guests: number;
  totalPrice: number;
  status: 'pending' | 'confirmed' | 'cancelled' | 'completed';
  createdAt: string;
  // Relations
  user?: User;
  room?: Room;
}

export interface Room {
  id: number;
  name: string;
  internalName: string;
  description: string;
  price: number;
  priceCurrency: 'SAR' | 'USD' | 'EUR' | 'GBP';
  priceUsd?: number;
  image: string;
  images?: string[];
  amenities: string[];
  capacity: number;
  rating: number;
  reviewCount: number;
  roomType: 'standard' | 'deluxe' | 'suite' | 'family';
  distanceToHaram: number;
  size: number;
  bedType: string;
  bedCount?: number;
  maxGuests?: number;
  bathroomCount?: number;
  hasBalcony?: boolean;
  hasKitchen?: boolean;
  hasBathroom?: boolean;
  mainImageIndex?: number;
  // Simple location field
  googleMapsLink?: string;
} 