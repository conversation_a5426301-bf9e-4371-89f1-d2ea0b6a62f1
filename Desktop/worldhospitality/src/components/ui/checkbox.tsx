'use client'

import { Check } from 'lucide-react'

interface CheckboxProps {
  checked: boolean
  onCheckedChange: (checked: boolean) => void
  className?: string
}

export const Checkbox = ({ checked, onCheckedChange, className }: CheckboxProps) => {
  return (
    <div
      className={`w-4 h-4 border-2 border-gray-300 rounded cursor-pointer flex items-center justify-center transition-colors ${
        checked ? 'bg-blue-600 border-blue-600' : 'hover:border-gray-400'
      } ${className}`}
      onClick={() => onCheckedChange(!checked)}
    >
      {checked && <Check className="w-3 h-3 text-white" />}
    </div>
  )
} 