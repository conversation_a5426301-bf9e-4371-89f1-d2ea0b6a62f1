'use client'

import { useState, useRef, useEffect } from 'react'
import { Calendar as CalendarIcon } from 'lucide-react'
import { Calendar } from './Calendar'
import { format } from 'date-fns'

interface DatePickerProps {
  selected?: Date
  onSelect: (date: Date) => void
  placeholder?: string
  minDate?: Date
  maxDate?: Date
  className?: string
  label?: string
}

export const DatePicker = ({ 
  selected, 
  onSelect, 
  placeholder = 'Select date',
  minDate,
  maxDate,
  className = '',
  label
}: DatePickerProps) => {
  const [isOpen, setIsOpen] = useState(false)
  const containerRef = useRef<HTMLDivElement>(null)

  // Close calendar when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const handleDateSelect = (date: Date) => {
    onSelect(date)
    setIsOpen(false)
  }

  const displayValue = selected ? format(selected, 'MMM dd, yyyy') : ''

  return (
    <div ref={containerRef} className={`relative ${className}`}>
      {label && (
        <label className="text-xs font-semibold text-gray-900 mb-1 block">
          {label}
        </label>
      )}
      
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className="w-full text-left bg-transparent border-none outline-none text-sm text-gray-500 hover:text-gray-700 transition-colors flex items-center gap-3 py-1"
      >
        <CalendarIcon className="h-5 w-5 text-gray-400" />
        <span className={`${displayValue ? 'text-gray-900 font-medium' : 'text-gray-400'} text-sm`}>
          {displayValue || placeholder}
        </span>
      </button>

      {isOpen && (
        <div className="absolute top-full left-0 z-50 mt-3">
          <Calendar
            selected={selected}
            onSelect={handleDateSelect}
            minDate={minDate}
            maxDate={maxDate}
          />
        </div>
      )}
    </div>
  )
}
