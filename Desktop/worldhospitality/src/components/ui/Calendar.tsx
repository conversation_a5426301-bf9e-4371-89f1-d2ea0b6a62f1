'use client'

import { useState } from 'react'
import { ChevronLeft, ChevronRight } from 'lucide-react'
import { format, startOfMonth, endOfMonth, eachDayOfInterval, isSameMonth, isSameDay, addMonths, subMonths, isToday, isBefore } from 'date-fns'

interface CalendarProps {
  selected?: Date
  onSelect: (date: Date) => void
  minDate?: Date
  maxDate?: Date
  className?: string
}

export const Calendar = ({ selected, onSelect, minDate, maxDate, className = '' }: CalendarProps) => {
  const [currentMonth, setCurrentMonth] = useState(selected || new Date())

  const monthStart = startOfMonth(currentMonth)
  const monthEnd = endOfMonth(currentMonth)
  const days = eachDayOfInterval({ start: monthStart, end: monthEnd })

  // Get the first day of the week for the month (to align calendar grid)
  const startDate = new Date(monthStart)
  startDate.setDate(startDate.getDate() - monthStart.getDay())

  // Get all days to display (including previous/next month days for complete weeks)
  const endDate = new Date(monthEnd)
  endDate.setDate(endDate.getDate() + (6 - monthEnd.getDay()))
  
  const allDays = eachDayOfInterval({ start: startDate, end: endDate })

  const isDateDisabled = (date: Date) => {
    if (minDate && isBefore(date, minDate)) return true
    if (maxDate && isBefore(maxDate, date)) return true
    return false
  }

  const handleDateClick = (date: Date) => {
    if (isDateDisabled(date)) return
    onSelect(date)
  }

  const goToPreviousMonth = () => {
    setCurrentMonth(subMonths(currentMonth, 1))
  }

  const goToNextMonth = () => {
    setCurrentMonth(addMonths(currentMonth, 1))
  }

  return (
    <div className={`bg-white border border-gray-200 rounded-xl shadow-xl p-6 min-w-[320px] ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <button
          type="button"
          onClick={goToPreviousMonth}
          className="p-3 hover:bg-gray-100 rounded-full transition-colors group"
        >
          <ChevronLeft className="h-5 w-5 text-gray-600 group-hover:text-gray-900" />
        </button>
        
        <h2 className="text-xl font-bold text-gray-900">
          {format(currentMonth, 'MMMM yyyy')}
        </h2>
        
        <button
          type="button"
          onClick={goToNextMonth}
          className="p-3 hover:bg-gray-100 rounded-full transition-colors group"
        >
          <ChevronRight className="h-5 w-5 text-gray-600 group-hover:text-gray-900" />
        </button>
      </div>

      {/* Days of week */}
      <div className="grid grid-cols-7 gap-2 mb-4">
        {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) => (
          <div key={day} className="text-center text-sm font-semibold text-gray-600 py-3">
            {day}
          </div>
        ))}
      </div>

      {/* Calendar grid */}
      <div className="grid grid-cols-7 gap-2">
        {allDays.map((day) => {
          const isCurrentMonth = isSameMonth(day, currentMonth)
          const isSelected = selected && isSameDay(day, selected)
          const isTodayDate = isToday(day)
          const isDisabled = isDateDisabled(day)

          return (
            <button
              key={day.toISOString()}
              type="button"
              onClick={() => handleDateClick(day)}
              disabled={isDisabled}
              className={`
                h-12 w-12 text-base rounded-xl transition-all duration-200 relative font-medium
                ${isCurrentMonth 
                  ? 'text-gray-900' 
                  : 'text-gray-300'
                }
                ${isSelected 
                  ? 'bg-blue-600 text-white font-bold shadow-lg scale-105 ring-2 ring-blue-200' 
                  : 'hover:bg-gray-100 hover:scale-105'
                }
                ${isTodayDate && !isSelected 
                  ? 'bg-blue-50 text-blue-600 font-bold ring-2 ring-blue-200' 
                  : ''
                }
                ${isDisabled 
                  ? 'opacity-40 cursor-not-allowed hover:bg-transparent hover:scale-100' 
                  : 'cursor-pointer'
                }
              `}
            >
              {format(day, 'd')}
              {isTodayDate && !isSelected && (
                <div className="absolute bottom-1 left-1/2 transform -translate-x-1/2 w-1.5 h-1.5 bg-blue-600 rounded-full"></div>
              )}
            </button>
          )
        })}
      </div>
    </div>
  )
}
