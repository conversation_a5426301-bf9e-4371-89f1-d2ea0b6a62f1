'use client'

import { useState, useEffect } from 'react'

interface SliderProps {
  value: number[]
  onValueChange: (value: number[]) => void
  min: number
  max: number
  step: number
  className?: string
}

export const Slider = ({ value, onValueChange, min, max, step, className }: SliderProps) => {
  const [isDragging, setIsDragging] = useState<number | null>(null)

  const handleMouseDown = (index: number) => (e: React.MouseEvent) => {
    setIsDragging(index)
    e.preventDefault()
  }

  const handleMouseMove = (e: MouseEvent) => {
    if (isDragging === null) return

    const slider = document.getElementById('slider-track')
    if (!slider) return

    const rect = slider.getBoundingClientRect()
    const percentage = Math.max(0, Math.min(1, (e.clientX - rect.left) / rect.width))
    const newValue = Math.round((min + percentage * (max - min)) / step) * step

    const newValues = [...value]
    newValues[isDragging] = Math.max(min, Math.min(max, newValue))

    // Ensure min <= max
    if (newValues.length === 2) {
      if (isDragging === 0 && newValues[0] > newValues[1]) {
        newValues[0] = newValues[1]
      } else if (isDragging === 1 && newValues[1] < newValues[0]) {
        newValues[1] = newValues[0]
      }
    }

    onValueChange(newValues)
  }

  const handleMouseUp = () => {
    setIsDragging(null)
  }

  useEffect(() => {
    if (isDragging !== null) {
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)
      return () => {
        document.removeEventListener('mousemove', handleMouseMove)
        document.removeEventListener('mouseup', handleMouseUp)
      }
    }
  }, [isDragging])

  const getPercentage = (val: number) => ((val - min) / (max - min)) * 100

  return (
    <div className={`relative w-full h-6 flex items-center ${className}`}>
      <div 
        id="slider-track"
        className="w-full h-2 bg-gray-200 rounded-full relative cursor-pointer"
      >
        {/* Track fill */}
        {value.length === 2 && (
          <div
            className="absolute h-2 bg-blue-600 rounded-full"
            style={{
              left: `${getPercentage(value[0])}%`,
              width: `${getPercentage(value[1]) - getPercentage(value[0])}%`
            }}
          />
        )}
        
        {/* Handles */}
        {value.map((val, index) => (
          <div
            key={index}
            className="absolute w-4 h-4 bg-blue-600 rounded-full border-2 border-white shadow-md cursor-pointer transform -translate-x-1/2 -translate-y-1/2 hover:scale-110 transition-transform"
            style={{ left: `${getPercentage(val)}%`, top: '50%' }}
            onMouseDown={handleMouseDown(index)}
          />
        ))}
      </div>
    </div>
  )
} 