'use client'

import { useState } from 'react'
import Image from 'next/image'
import { Room } from '@/types'
import { ImageGalleryModal } from './ImageGalleryModal'
import { Star, MapPin } from 'lucide-react'
import { useTranslation } from 'react-i18next'
import '@/lib/i18n.client'

interface RoomDetailClientProps {
  room: Room
  images: string[]
}

export function RoomDetailClient({ room, images }: RoomDetailClientProps) {
  const { t, i18n } = useTranslation('common');
  const [galleryOpen, setGalleryOpen] = useState(false)
  const [selectedImageIndex, setSelectedImageIndex] = useState(0)

  const openGallery = (index: number = 0) => {
    setSelectedImageIndex(index)
    setGalleryOpen(true)
  }

  return (
    <>
      {/* Property Title */}
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          {room.name}
        </h1>
        <div className="flex items-center gap-4 text-sm text-gray-600">
          <div className="flex items-center gap-1">
            <Star className="h-4 w-4 text-yellow-400 fill-current" />
            <span className="font-semibold">{room.rating}</span>
            <span>({room.reviewCount} {t('room.review', { count: room.reviewCount })})</span>
          </div>
          <div className="flex items-center gap-1">
            <MapPin className="h-4 w-4" />
            <span>{t('room.city_country')}</span>
          </div>
        </div>
      </div>

      {/* Clickable Image Gallery */}
      <div className="grid grid-cols-4 gap-2 mb-8 h-96 rounded-xl overflow-hidden cursor-pointer">
        {/* Main Image - Clickable */}
        <div 
          className="col-span-2 row-span-2 relative hover:brightness-110 transition-all"
          onClick={() => openGallery(0)}
        >
          <Image
            src={images[0]}
            alt={room.name}
            fill
            className="object-cover"
            sizes="(max-width: 768px) 100vw, 50vw"
          />
        </div>

        {/* Side Images - Clickable */}
        {images.slice(1, 5).map((image, index) => (
          <div 
            key={index} 
            className="relative hover:brightness-110 transition-all"
            onClick={() => openGallery(index + 1)}
          >
            <Image
              src={image}
              alt={`${room.name} - Image ${index + 2}`}
              fill
              className="object-cover"
              sizes="(max-width: 768px) 50vw, 25vw"
            />
            {/* Show all photos overlay on last image */}
            {index === 3 && images.length > 5 && (
              <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                <button className="text-white font-semibold text-sm hover:text-gray-200 transition-colors">
                  {t('room.more_photos', { count: images.length - 5 })}
                </button>
              </div>
            )}
          </div>
        ))}

        {/* Show all photos button */}
        <button
          onClick={() => openGallery(0)}
          className="absolute bottom-4 right-4 bg-white border border-gray-300 px-4 py-2 rounded-lg font-medium text-gray-700 hover:bg-gray-50 transition-colors shadow-sm"
        >
          {t('room.show_all_photos', { count: images.length })}
        </button>
      </div>

      {/* Image Gallery Modal */}
      <ImageGalleryModal
        images={images}
        isOpen={galleryOpen}
        onClose={() => setGalleryOpen(false)}
        initialIndex={selectedImageIndex}
        roomName={room.name}
      />
    </>
  )
} 