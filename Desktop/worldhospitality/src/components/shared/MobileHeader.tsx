'use client'

import Link from 'next/link'
import { Globe, Menu, X } from 'lucide-react'
import { useState, useEffect } from 'react'

interface MobileHeaderProps {
  lng: string;
  roomsText: string;
  bookNowText: string;
  toggleLanguageText: string;
}

export const MobileHeader = ({ lng, roomsText, bookNowText, toggleLanguageText }: MobileHeaderProps) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const isRTL = lng === 'ar'
  
  // Close menu when clicking outside or on links
  useEffect(() => {
    const handleClickOutside = () => setIsMenuOpen(false)
    if (isMenuOpen) {
      document.addEventListener('click', handleClickOutside)
      return () => document.removeEventListener('click', handleClickOutside)
    }
  }, [isMenuOpen])
  
  return (
    <>
      {/* Mobile Menu Button */}
      <button
        onClick={(e) => {
          e.stopPropagation()
          setIsMenuOpen(!isMenuOpen)
        }}
        className="md:hidden text-white hover:text-white/80 transition-colors p-2 bg-white/10 backdrop-blur-sm rounded-lg border border-white/20 hover:border-white/40"
        aria-label="Toggle menu"
      >
        {isMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
      </button>

      {/* Mobile Menu */}
      {isMenuOpen && (
        <div className="absolute top-full left-0 right-0 md:hidden bg-slate-900/95 backdrop-blur-lg border-t border-white/20 shadow-2xl">
          <div className="container mx-auto py-6 px-6 space-y-4">
            <Link 
              href={`/${lng}/rooms`} 
              className="block text-white hover:text-blue-300 transition-colors font-medium py-3 px-4 rounded-lg hover:bg-white/10"
              onClick={() => setIsMenuOpen(false)}
            >
              {roomsText}
            </Link>
            <Link 
              href={`/${lng}/rooms`} 
              className="block text-white hover:text-blue-300 transition-colors font-medium py-3 px-4 rounded-lg hover:bg-white/10"
              onClick={() => setIsMenuOpen(false)}
            >
              {bookNowText}
            </Link>
            <div className="pt-2 border-t border-white/20">
              <Link 
                href={lng === 'en' ? '/ar' : '/en'} 
                className={`flex items-center text-white hover:text-blue-300 transition-colors py-3 px-4 rounded-lg hover:bg-white/10 ${isRTL ? 'flex-row-reverse' : ''}`}
                onClick={() => setIsMenuOpen(false)}
              >
                <Globe className={`h-5 w-5 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                <span className="font-medium">{toggleLanguageText}</span>
              </Link>
            </div>
          </div>
        </div>
      )}
    </>
  )
} 