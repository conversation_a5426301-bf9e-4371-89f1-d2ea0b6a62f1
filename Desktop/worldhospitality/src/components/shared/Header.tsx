import Link from 'next/link'
import { Globe } from 'lucide-react'
import { useTranslation } from '../../lib/i18n'

export const Header = async ({ lng }: { lng: string }) => {
  // eslint-disable-next-line react-hooks/rules-of-hooks
  const { t } = await useTranslation(lng)
  const isRTL = lng === 'ar'
  
  return (
    <header className="absolute top-0 left-0 right-0 z-50 bg-white/95 backdrop-blur-sm border-b border-gray-100">
      <nav className="container mx-auto flex items-center justify-between py-3 px-6">
        {/* Logo */}
        <Link href={`/${lng}`} className="flex items-center">
          <div className="text-lg font-semibold text-blue-600 hover:text-blue-700 transition-colors">
            World Hospitality
          </div>
        </Link>
        
        {/* Desktop Language Toggle */}
        <div className="hidden md:flex items-center">
          <Link 
            href={lng === 'en' ? '/ar' : '/en'} 
            className={`group inline-flex items-center px-3 py-1.5 text-gray-700 hover:text-gray-900 transition-colors text-sm font-medium rounded-full hover:bg-gray-50 ${isRTL ? 'flex-row-reverse' : ''}`}
          >
            <Globe className={`h-4 w-4 ${isRTL ? 'ml-1.5' : 'mr-1.5'}`} />
            <span>{t('toggle_language')}</span>
          </Link>
        </div>

        {/* Mobile Language Toggle */}
        <div className="md:hidden">
          <Link 
            href={lng === 'en' ? '/ar' : '/en'} 
            className={`group inline-flex items-center px-2 py-1 text-gray-700 hover:text-gray-900 transition-colors text-sm rounded-full hover:bg-gray-50 ${isRTL ? 'flex-row-reverse' : ''}`}
          >
            <Globe className={`h-4 w-4 ${isRTL ? 'ml-1' : 'mr-1'}`} />
            <span className="text-xs">{t('toggle_language')}</span>
          </Link>
        </div>
      </nav>
    </header>
  )
} 