import { SearchBar } from './SearchBar'
import { useTranslation } from '@/lib/i18n'

interface SearchBarWrapperProps {
  lng: string
}

export const SearchBarWrapper = async ({ lng }: SearchBarWrapperProps) => {
  const { t } = await useTranslation(lng, 'common')
  const isRTL = lng === 'ar'
  
  // Pre-translate all the strings needed by SearchBar
  const translations = {
    where: t('homepage.search.where'),
    where_placeholder: t('homepage.search.where_placeholder'),
    checkin: t('homepage.search.checkin'),
    checkout: t('homepage.search.checkout'),
    who: t('homepage.search.who'),
    guest: t('homepage.search.guest'),
    guests: t('homepage.search.guests')
  }

  return (
    <SearchBar 
      lng={lng} 
      translations={translations}
      isRTL={isRTL} 
    />
  )
} 