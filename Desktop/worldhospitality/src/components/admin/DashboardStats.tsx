'use client'

import { DollarSign, Users, Calendar, Home, TrendingUp, TrendingDown } from 'lucide-react'

interface StatCardProps {
  title: string
  value: string
  change: string
  changeType: 'positive' | 'negative'
  icon: React.ReactNode
}

const StatCard = ({ title, value, change, changeType, icon }: StatCardProps) => (
  <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
    <div className="flex items-center justify-between">
      <div>
        <p className="text-sm font-medium text-gray-600">{title}</p>
        <p className="text-3xl font-bold text-gray-900 mt-2">{value}</p>
        <div className={`flex items-center mt-2 ${changeType === 'positive' ? 'text-green-600' : 'text-red-600'}`}>
          {changeType === 'positive' ? (
            <TrendingUp className="h-4 w-4 mr-1" />
          ) : (
            <TrendingDown className="h-4 w-4 mr-1" />
          )}
          <span className="text-sm font-medium">{change}</span>
          <span className="text-sm text-gray-500 ml-1">vs last month</span>
        </div>
      </div>
      <div className="bg-blue-50 p-3 rounded-lg">
        {icon}
      </div>
    </div>
  </div>
)

export const DashboardStats = () => {
  const stats = [
    {
      title: "Total Revenue",
      value: "$24,580",
      change: "+12.5%",
      changeType: "positive" as const,
      icon: <DollarSign className="h-6 w-6 text-blue-600" />
    },
    {
      title: "Active Bookings",
      value: "156",
      change: "+8.2%",
      changeType: "positive" as const,
      icon: <Calendar className="h-6 w-6 text-blue-600" />
    },
    {
      title: "Total Guests",
      value: "2,847",
      change: "+15.3%",
      changeType: "positive" as const,
      icon: <Users className="h-6 w-6 text-blue-600" />
    },
    {
      title: "Available Rooms",
      value: "8",
      change: "-2 rooms",
      changeType: "negative" as const,
      icon: <Home className="h-6 w-6 text-blue-600" />
    }
  ]

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      {stats.map((stat, index) => (
        <StatCard
          key={index}
          title={stat.title}
          value={stat.value}
          change={stat.change}
          changeType={stat.changeType}
          icon={stat.icon}
        />
      ))}
    </div>
  )
} 