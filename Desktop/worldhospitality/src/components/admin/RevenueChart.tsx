'use client'

import { TrendingUp } from 'lucide-react'

export const RevenueChart = () => {
  const monthlyData = [
    { month: 'Jan', revenue: 18500, percentage: 65 },
    { month: 'Feb', revenue: 22300, percentage: 78 },
    { month: 'Mar', revenue: 19800, percentage: 69 },
    { month: 'Apr', revenue: 26100, percentage: 91 },
    { month: 'May', revenue: 24800, percentage: 87 },
    { month: 'Jun', revenue: 28700, percentage: 100 },
  ]

  return (
    <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">Revenue Overview</h3>
          <p className="text-sm text-gray-600">Monthly revenue for the last 6 months</p>
        </div>
        <div className="flex items-center text-green-600">
          <TrendingUp className="h-4 w-4 mr-1" />
          <span className="text-sm font-medium">+18.2%</span>
        </div>
      </div>

      {/* Chart */}
      <div className="relative h-64">
        <div className="flex items-end justify-between h-full space-x-2">
          {monthlyData.map((data, index) => (
            <div key={index} className="flex-1 flex flex-col items-center">
              <div className="w-full relative">
                <div 
                  className="w-full bg-blue-600 rounded-t-md transition-all duration-500 ease-out hover:bg-blue-700"
                  style={{ height: `${(data.percentage / 100) * 200}px` }}
                ></div>
                <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 text-xs font-medium text-gray-700">
                  ${(data.revenue / 1000).toFixed(0)}k
                </div>
              </div>
              <div className="mt-3 text-xs font-medium text-gray-600">
                {data.month}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-3 gap-4 mt-6 pt-6 border-t border-gray-100">
        <div className="text-center">
          <p className="text-lg font-bold text-gray-900">$28.7k</p>
          <p className="text-xs text-gray-600">This Month</p>
        </div>
        <div className="text-center">
          <p className="text-lg font-bold text-gray-900">$140.2k</p>
          <p className="text-xs text-gray-600">Last 6 Months</p>
        </div>
        <div className="text-center">
          <p className="text-lg font-bold text-green-600">+18.2%</p>
          <p className="text-xs text-gray-600">Growth Rate</p>
        </div>
      </div>
    </div>
  )
} 