'use client'

import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog"
import { Room } from '@/types'
import { Textarea } from '@/components/ui/textarea'

const formSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  price: z.coerce.number().min(1, 'Price must be a positive number'),
  description: z.string().min(1, 'Description is required'),
  amenities: z.string().min(1, 'Amenities are required'),
  capacity: z.coerce.number().min(1, 'Capacity must be at least 1'),
  
  // New enhanced fields
  rating: z.coerce.number().min(1).max(5, 'Rating must be between 1 and 5'),
  reviewCount: z.coerce.number().min(0, 'Review count must be 0 or more'),
  roomType: z.enum(['standard', 'deluxe', 'suite', 'family']),
  distanceToHaram: z.coerce.number().min(1, 'Distance must be positive'),
  size: z.coerce.number().min(1, 'Size must be positive'),
  bedType: z.string().min(1, 'Bed type is required'),
  hasBalcony: z.boolean(),
  hasKitchen: z.boolean(),
  hasBathroom: z.boolean(),
  
  image: z.any().optional(),
})

interface RoomFormProps {
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
  room?: Room | null;
  onSave: (data: z.infer<typeof formSchema>, imageFile?: File) => void;
}

export function RoomForm({ isOpen, setIsOpen, room, onSave }: RoomFormProps) {
  const { register, handleSubmit, formState: { errors }, setValue, watch } = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: room?.name || '',
      price: room?.price || 0,
      description: room?.description || '',
      amenities: room?.amenities.join(', ') || '',
      capacity: room?.capacity || 2,
      rating: room?.rating || 4.0,
      reviewCount: room?.reviewCount || 0,
      roomType: room?.roomType || 'standard',
      distanceToHaram: room?.distanceToHaram || 500,
      size: room?.size || 25,
      bedType: room?.bedType || 'Double',
      hasBalcony: room?.hasBalcony || false,
      hasKitchen: room?.hasKitchen || false,
      hasBathroom: room?.hasBathroom || true,
    },
  })
  
  const onSubmit = (data: z.infer<typeof formSchema>) => {
    const imageFile = data.image?.[0]
    onSave(data, imageFile)
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{room ? 'Edit Room' : 'Add New Room'}</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="md:col-span-2">
              <h3 className="text-lg font-semibold mb-4">Basic Information</h3>
            </div>
            
            <div>
              <Label htmlFor="name">Room Name *</Label>
              <Input id="name" {...register('name')} placeholder="e.g., Deluxe Suite with Kaaba View" />
              {errors.name && <p className="text-red-500 text-xs mt-1">{errors.name.message}</p>}
            </div>
            
            <div>
              <Label htmlFor="price">Price per Night (USD) *</Label>
              <Input id="price" type="number" {...register('price')} placeholder="450" />
              {errors.price && <p className="text-red-500 text-xs mt-1">{errors.price.message}</p>}
            </div>
            
            <div className="md:col-span-2">
              <Label htmlFor="description">Description *</Label>
              <Textarea 
                id="description" 
                {...register('description')} 
                placeholder="Describe the room features, location, and amenities..."
                rows={3}
              />
              {errors.description && <p className="text-red-500 text-xs mt-1">{errors.description.message}</p>}
            </div>
            
            <div className="md:col-span-2">
              <Label htmlFor="amenities">Amenities (comma-separated) *</Label>
              <Input 
                id="amenities" 
                {...register('amenities')} 
                placeholder="Free WiFi, Air Conditioning, Prayer Mat, Balcony"
              />
              {errors.amenities && <p className="text-red-500 text-xs mt-1">{errors.amenities.message}</p>}
            </div>
          </div>

          {/* Room Details */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="md:col-span-3">
              <h3 className="text-lg font-semibold mb-4">Room Details</h3>
            </div>
            
            <div>
              <Label htmlFor="roomType">Room Type *</Label>
              <select 
                id="roomType" 
                {...register('roomType')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="standard">Standard</option>
                <option value="deluxe">Deluxe</option>
                <option value="suite">Suite</option>
                <option value="family">Family</option>
              </select>
              {errors.roomType && <p className="text-red-500 text-xs mt-1">{errors.roomType.message}</p>}
            </div>
            
            <div>
              <Label htmlFor="capacity">Guest Capacity *</Label>
              <Input id="capacity" type="number" min="1" max="10" {...register('capacity')} />
              {errors.capacity && <p className="text-red-500 text-xs mt-1">{errors.capacity.message}</p>}
            </div>
            
            <div>
              <Label htmlFor="size">Size (sqm) *</Label>
              <Input id="size" type="number" min="1" {...register('size')} placeholder="35" />
              {errors.size && <p className="text-red-500 text-xs mt-1">{errors.size.message}</p>}
            </div>
            
            <div>
              <Label htmlFor="bedType">Bed Type *</Label>
              <Input id="bedType" {...register('bedType')} placeholder="King, Queen, Twin, etc." />
              {errors.bedType && <p className="text-red-500 text-xs mt-1">{errors.bedType.message}</p>}
            </div>
            
            <div>
              <Label htmlFor="distanceToHaram">Distance to Haram (meters) *</Label>
              <Input id="distanceToHaram" type="number" min="1" {...register('distanceToHaram')} placeholder="150" />
              {errors.distanceToHaram && <p className="text-red-500 text-xs mt-1">{errors.distanceToHaram.message}</p>}
            </div>
          </div>

          {/* Rating & Reviews */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="md:col-span-2">
              <h3 className="text-lg font-semibold mb-4">Rating & Reviews</h3>
            </div>
            
            <div>
              <Label htmlFor="rating">Rating (1-5) *</Label>
              <Input 
                id="rating" 
                type="number" 
                min="1" 
                max="5" 
                step="0.1" 
                {...register('rating')} 
                placeholder="4.5" 
              />
              {errors.rating && <p className="text-red-500 text-xs mt-1">{errors.rating.message}</p>}
            </div>
            
            <div>
              <Label htmlFor="reviewCount">Number of Reviews *</Label>
              <Input id="reviewCount" type="number" min="0" {...register('reviewCount')} placeholder="127" />
              {errors.reviewCount && <p className="text-red-500 text-xs mt-1">{errors.reviewCount.message}</p>}
            </div>
          </div>

          {/* Features */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Room Features</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="flex items-center space-x-2">
                <input 
                  type="checkbox" 
                  id="hasBalcony" 
                  {...register('hasBalcony')}
                  className="w-4 h-4 text-blue-600 rounded focus:ring-blue-500"
                />
                <Label htmlFor="hasBalcony">Has Balcony</Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <input 
                  type="checkbox" 
                  id="hasKitchen" 
                  {...register('hasKitchen')}
                  className="w-4 h-4 text-blue-600 rounded focus:ring-blue-500"
                />
                <Label htmlFor="hasKitchen">Has Kitchen/Kitchenette</Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <input 
                  type="checkbox" 
                  id="hasBathroom" 
                  {...register('hasBathroom')}
                  className="w-4 h-4 text-blue-600 rounded focus:ring-blue-500"
                />
                <Label htmlFor="hasBathroom">Has Private Bathroom</Label>
              </div>
            </div>
          </div>

          {/* Image Upload */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Room Image</h3>
            <div>
              <Label htmlFor="image">Upload Image</Label>
              <Input 
                id="image" 
                type="file" 
                accept="image/*"
                {...register('image')} 
                className="file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
              />
              <p className="text-xs text-gray-500 mt-1">Upload a high-quality image of the room (JPG, PNG)</p>
            </div>
          </div>

          <DialogFooter className="gap-2">
            <Button type="button" variant="outline" onClick={() => setIsOpen(false)}>
              Cancel
            </Button>
            <Button type="submit" className="bg-blue-600 hover:bg-blue-700">
              {room ? 'Update Room' : 'Add Room'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
} 