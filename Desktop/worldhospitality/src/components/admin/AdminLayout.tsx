import { AdminSidebar } from './AdminSidebar'
import { Toaster } from "@/components/ui/sonner"

interface AdminLayoutProps {
  children: React.ReactNode;
  lng: string;
}

export function AdminLayout({ children, lng }: AdminLayoutProps) {
  return (
    <div className="flex h-screen bg-gray-100">
      <AdminSidebar lng={lng} />
      <main className="flex-1 p-8 overflow-y-auto">
        {children}
      </main>
      <Toaster richColors />
    </div>
  )
} 