'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { X, Upload, Plus, Star, StarOff } from 'lucide-react'

interface ImageUploadWithMainSelectionProps {
  selectedImages: File[]
  onImagesChange: (images: File[]) => void
  mainImageIndex: number
  onMainImageChange: (index: number) => void
  existingImages?: string[]
  existingMainIndex?: number
}

export function ImageUploadWithMainSelection({
  selectedImages,
  onImagesChange,
  mainImageIndex,
  onMainImageChange,
  existingImages = [],
  existingMainIndex = 0
}: ImageUploadWithMainSelectionProps) {
  const [allImages, setAllImages] = useState<Array<{ url: string; isNew: boolean; file?: File }>>([])

  // Initialize with existing images
  useEffect(() => {
    const existing = existingImages.map(url => ({ url, isNew: false }))
    const newImages = selectedImages.map(file => ({
      url: URL.createObjectURL(file),
      isNew: true,
      file
    }))
    setAllImages([...existing, ...newImages])
  }, [existingImages, selectedImages])

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const filesArray = Array.from(e.target.files)
      onImagesChange([...selectedImages, ...filesArray])
    }
  }

  const removeImage = (index: number) => {
    const imageToRemove = allImages[index]
    
    if (imageToRemove.isNew) {
      // Remove from new images
      const newSelectedImages = selectedImages.filter((_, i) => {
        const newImageIndex = existingImages.length + i
        return newImageIndex !== index
      })
      onImagesChange(newSelectedImages)
    } else {
      // For existing images, we'd need to handle deletion differently
      // For now, just update the display
      const updatedImages = allImages.filter((_, i) => i !== index)
      setAllImages(updatedImages)
    }

    // Adjust main image index if needed
    if (index === mainImageIndex) {
      onMainImageChange(0) // Set first image as main if current main is removed
    } else if (index < mainImageIndex) {
      onMainImageChange(mainImageIndex - 1)
    }
  }

  const setAsMainImage = (index: number) => {
    onMainImageChange(index)
  }

  return (
    <div>
      <h3 className="text-lg font-semibold mb-4 text-rose-600">Room Images</h3>
      
      {/* Upload Area */}
      <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 mb-4">
        <div className="text-center">
          <Upload className="mx-auto h-12 w-12 text-gray-400" />
          <div className="mt-4">
            <label htmlFor="images" className="cursor-pointer">
              <span className="mt-2 block text-sm font-medium text-gray-900">
                Upload multiple room images
              </span>
              <span className="text-xs text-gray-500">
                PNG, JPG, WEBP up to 10MB each. Select multiple files.
              </span>
            </label>
            <Input
              id="images"
              type="file"
              multiple
              accept="image/*"
              onChange={handleImageChange}
              className="hidden"
            />
          </div>
          <Button 
            type="button" 
            variant="outline" 
            onClick={() => document.getElementById('images')?.click()}
            className="mt-4"
          >
            <Plus className="w-4 h-4 mr-2" />
            Select Images
          </Button>
        </div>
      </div>

      {/* Images Preview with Main Photo Selection */}
      {allImages.length > 0 && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h4 className="font-medium">
              Images ({allImages.length})
              {allImages.length > 0 && (
                <span className="text-sm text-gray-500 ml-2">
                  Click the star to set as main photo
                </span>
              )}
            </h4>
          </div>

          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
            {allImages.map((image, index) => (
              <div key={index} className="relative group">
                {/* Main Photo Badge */}
                {index === mainImageIndex && (
                  <div className="absolute -top-2 -left-2 z-10 bg-yellow-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                    MAIN
                  </div>
                )}

                {/* Image */}
                <div className="relative">
                  <img
                    src={image.url}
                    alt={`Preview ${index + 1}`}
                    className={`w-full h-32 object-cover rounded border-2 transition-all ${
                      index === mainImageIndex 
                        ? 'border-yellow-400 shadow-lg' 
                        : 'border-gray-200 group-hover:border-gray-300'
                    }`}
                  />

                  {/* Overlay with actions */}
                  <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all rounded flex items-center justify-center">
                    <div className="opacity-0 group-hover:opacity-100 transition-opacity flex gap-2">
                      {/* Set as main button */}
                      <button
                        type="button"
                        onClick={() => setAsMainImage(index)}
                        className={`p-2 rounded-full transition-all ${
                          index === mainImageIndex 
                            ? 'bg-yellow-500 text-white' 
                            : 'bg-white text-gray-700 hover:bg-yellow-50'
                        }`}
                        title={index === mainImageIndex ? 'Main photo' : 'Set as main photo'}
                      >
                        {index === mainImageIndex ? (
                          <Star className="w-4 h-4 fill-current" />
                        ) : (
                          <StarOff className="w-4 h-4" />
                        )}
                      </button>

                      {/* Remove button */}
                      <button
                        type="button"
                        onClick={() => removeImage(index)}
                        className="p-2 bg-red-500 text-white rounded-full hover:bg-red-600 transition-all"
                        title="Remove image"
                      >
                        <X className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>

                {/* Image info */}
                <div className="mt-2">
                  {image.isNew ? (
                    <p className="text-xs text-green-600 font-medium">NEW</p>
                  ) : (
                    <p className="text-xs text-blue-600 font-medium">EXISTING</p>
                  )}
                  <p className="text-xs text-gray-500 truncate">
                    {image.file ? image.file.name : `Image ${index + 1}`}
                  </p>
                </div>
              </div>
            ))}
          </div>

          {/* Main Photo Info */}
          {allImages.length > 0 && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
              <div className="flex items-center gap-2">
                <Star className="w-4 h-4 text-yellow-600 fill-current" />
                <span className="text-sm font-medium text-yellow-800">
                  Main Photo: Image {mainImageIndex + 1}
                </span>
              </div>
              <p className="text-xs text-yellow-700 mt-1">
                This will be the primary image shown to guests on listings and search results.
              </p>
            </div>
          )}
        </div>
      )}
    </div>
  )
} 