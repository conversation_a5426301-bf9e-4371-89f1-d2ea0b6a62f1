'use client'

import { Plus, Users, Home, Calendar, Settings, BarChart3, Mail, Download } from 'lucide-react'

export const QuickActions = () => {
  const actions = [
    {
      title: 'Add New Room',
      description: 'Create a new hotel room listing',
      icon: <Plus className="h-5 w-5 text-blue-600" />,
      bgColor: 'bg-blue-50',
      href: '/en/admin/rooms'
    },
    {
      title: 'View Bookings',
      description: 'Check recent reservations',
      icon: <Calendar className="h-5 w-5 text-green-600" />,
      bgColor: 'bg-green-50',
      href: '/en/admin/bookings'
    },
    {
      title: 'Manage Users',
      description: 'View and manage user accounts',
      icon: <Users className="h-5 w-5 text-purple-600" />,
      bgColor: 'bg-purple-50',
      href: '/en/admin/users'
    },
    {
      title: 'Generate Report',
      description: 'Create analytics report',
      icon: <BarChart3 className="h-5 w-5 text-orange-600" />,
      bgColor: 'bg-orange-50',
      href: '#'
    },
    {
      title: 'Send Newsletter',
      description: 'Send email to all guests',
      icon: <Mail className="h-5 w-5 text-pink-600" />,
      bgColor: 'bg-pink-50',
      href: '#'
    },
    {
      title: 'System Settings',
      description: 'Configure system preferences',
      icon: <Settings className="h-5 w-5 text-gray-600" />,
      bgColor: 'bg-gray-50',
      href: '#'
    }
  ]

  return (
    <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">Quick Actions</h3>
          <p className="text-sm text-gray-600">Common tasks and shortcuts</p>
        </div>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        {actions.map((action, index) => (
          <a
            key={index}
            href={action.href}
            className="flex items-center space-x-3 p-4 rounded-lg border border-gray-100 hover:border-blue-200 hover:bg-blue-50/50 transition-all duration-200 group"
          >
            <div className={`flex-shrink-0 w-10 h-10 ${action.bgColor} rounded-lg flex items-center justify-center group-hover:scale-105 transition-transform`}>
              {action.icon}
            </div>
            
            <div className="flex-1 min-w-0">
              <h4 className="text-sm font-medium text-gray-900 group-hover:text-blue-700">
                {action.title}
              </h4>
              <p className="text-xs text-gray-600 mt-1">
                {action.description}
              </p>
            </div>
          </a>
        ))}
      </div>

      {/* Today's Summary */}
      <div className="mt-6 pt-6 border-t border-gray-100">
        <h4 className="text-sm font-semibold text-gray-900 mb-3">Today's Summary</h4>
        <div className="grid grid-cols-2 gap-4">
          <div className="text-center">
            <p className="text-lg font-bold text-blue-600">12</p>
            <p className="text-xs text-gray-600">New Bookings</p>
          </div>
          <div className="text-center">
            <p className="text-lg font-bold text-green-600">8</p>
            <p className="text-xs text-gray-600">Check-ins</p>
          </div>
          <div className="text-center">
            <p className="text-lg font-bold text-purple-600">5</p>
            <p className="text-xs text-gray-600">Check-outs</p>
          </div>
          <div className="text-center">
            <p className="text-lg font-bold text-orange-600">$2.4k</p>
            <p className="text-xs text-gray-600">Revenue</p>
          </div>
        </div>
      </div>
    </div>
  )
} 