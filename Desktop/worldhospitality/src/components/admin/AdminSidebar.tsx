'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { Home, BedDouble, CalendarCheck } from 'lucide-react'
import { cn } from '@/lib/utils'

export function AdminSidebar({ lng }: { lng: string }) {
  const pathname = usePathname()

  const navLinks = [
    { href: `/${lng}/admin/dashboard`, label: 'Dashboard', icon: Home },
    { href: `/${lng}/admin/rooms`, label: 'Rooms', icon: BedDouble },
    { href: `/${lng}/admin/bookings`, label: 'Bookings', icon: CalendarCheck },
  ]

  return (
    <aside className="w-64 flex-shrink-0 border-r border-gray-200 bg-gray-50 flex flex-col">
      <div className="h-16 flex items-center justify-center border-b border-gray-200">
        <h1 className="text-xl font-bold text-blue-900">WorldHospitality</h1>
      </div>
      <nav className="flex-1 px-4 py-6 space-y-2">
        {navLinks.map((link) => (
          <Link
            key={link.href}
            href={link.href}
            className={cn(
              'flex items-center px-4 py-2 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors',
              pathname === link.href && 'bg-blue-100 text-blue-900 font-semibold'
            )}
          >
            <link.icon className="w-5 h-5 mr-3" />
            {link.label}
          </Link>
        ))}
      </nav>
    </aside>
  )
} 