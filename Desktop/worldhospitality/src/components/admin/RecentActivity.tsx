'use client'

import { Calendar, Users, CheckCircle, AlertCircle, Clock } from 'lucide-react'

export const RecentActivity = () => {
  const activities = [
    {
      id: 1,
      type: 'booking',
      title: 'New booking received',
      description: '<PERSON> booked Deluxe Suite for 3 nights',
      time: '2 minutes ago',
      icon: <Calendar className="h-4 w-4 text-blue-600" />,
      bgColor: 'bg-blue-50'
    },
    {
      id: 2,
      type: 'checkin',
      title: 'Guest checked in',
      description: '<PERSON> checked into Premium Room #201',
      time: '15 minutes ago',
      icon: <CheckCircle className="h-4 w-4 text-green-600" />,
      bgColor: 'bg-green-50'
    },
    {
      id: 3,
      type: 'booking',
      title: 'Booking cancelled',
      description: 'Mohammad K. cancelled Family Suite booking',
      time: '1 hour ago',
      icon: <AlertCircle className="h-4 w-4 text-red-600" />,
      bgColor: 'bg-red-50'
    },
    {
      id: 4,
      type: 'guest',
      title: 'New guest registered',
      description: '<PERSON><PERSON> A. created an account',
      time: '2 hours ago',
      icon: <Users className="h-4 w-4 text-purple-600" />,
      bgColor: 'bg-purple-50'
    },
    {
      id: 5,
      type: 'booking',
      title: 'Payment received',
      description: '$450 payment for booking #WH123456',
      time: '3 hours ago',
      icon: <CheckCircle className="h-4 w-4 text-green-600" />,
      bgColor: 'bg-green-50'
    },
    {
      id: 6,
      type: 'system',
      title: 'Room maintenance completed',
      description: 'Room #305 maintenance completed and ready',
      time: '4 hours ago',
      icon: <Clock className="h-4 w-4 text-blue-600" />,
      bgColor: 'bg-blue-50'
    }
  ]

  return (
    <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">Recent Activity</h3>
          <p className="text-sm text-gray-600">Latest updates and events</p>
        </div>
        <button className="text-blue-600 text-sm font-medium hover:text-blue-700">
          View All
        </button>
      </div>

      <div className="space-y-4 max-h-96 overflow-y-auto">
        {activities.map((activity) => (
          <div key={activity.id} className="flex items-start space-x-3">
            <div className={`flex-shrink-0 w-8 h-8 ${activity.bgColor} rounded-lg flex items-center justify-center`}>
              {activity.icon}
            </div>
            
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between">
                <h4 className="text-sm font-medium text-gray-900">
                  {activity.title}
                </h4>
                <span className="text-xs text-gray-500">{activity.time}</span>
              </div>
              <p className="text-sm text-gray-600 mt-1">
                {activity.description}
              </p>
            </div>
          </div>
        ))}
      </div>

      <div className="mt-6 pt-4 border-t border-gray-100">
        <div className="flex items-center justify-center">
          <button className="text-sm text-blue-600 hover:text-blue-700 font-medium">
            Load more activities
          </button>
        </div>
      </div>
    </div>
  )
} 