'use client'

import { Star, TrendingUp } from 'lucide-react'

export const TopRooms = () => {
  const topRooms = [
    {
      id: 1,
      name: "Deluxe Suite with Kaaba View",
      bookings: 45,
      revenue: 18500,
      rating: 4.9,
      change: "+12%",
      image: "https://images.unsplash.com/photo-1611892440504-42a792e24d32?w=300&h=200&fit=crop"
    },
    {
      id: 2,
      name: "Premium Room near Haram",
      bookings: 38,
      revenue: 14200,
      rating: 4.8,
      change: "+8%",
      image: "https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=300&h=200&fit=crop"
    },
    {
      id: 3,
      name: "Family Suite with Balcony",
      bookings: 29,
      revenue: 11800,
      rating: 4.7,
      change: "+15%",
      image: "https://images.unsplash.com/photo-1587316474091-ae1cf1e8f5a5?w=300&h=200&fit=crop"
    },
    {
      id: 4,
      name: "Standard Room - City View",
      bookings: 52,
      revenue: 9600,
      rating: 4.6,
      change: "+5%",
      image: "https://images.unsplash.com/photo-1631049307264-da0ec9d70304?w=300&h=200&fit=crop"
    }
  ]

  return (
    <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">Top Performing Rooms</h3>
          <p className="text-sm text-gray-600">Best rooms by revenue this month</p>
        </div>
        <button className="text-blue-600 text-sm font-medium hover:text-blue-700">
          View All
        </button>
      </div>

      <div className="space-y-4">
        {topRooms.map((room, index) => (
          <div key={room.id} className="flex items-center space-x-4 p-3 rounded-lg hover:bg-gray-50 transition-colors">
            <div className="flex-shrink-0">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <span className="text-blue-600 font-bold text-sm">#{index + 1}</span>
              </div>
            </div>
            
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between">
                <h4 className="text-sm font-medium text-gray-900 truncate">
                  {room.name}
                </h4>
                <div className="flex items-center text-green-600 text-xs">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  {room.change}
                </div>
              </div>
              
              <div className="flex items-center justify-between mt-1">
                <div className="flex items-center space-x-4 text-xs text-gray-600">
                  <span>{room.bookings} bookings</span>
                  <span>${(room.revenue / 1000).toFixed(1)}k revenue</span>
                </div>
                
                <div className="flex items-center">
                  <Star className="h-3 w-3 text-yellow-400 fill-current" />
                  <span className="text-xs text-gray-600 ml-1">{room.rating}</span>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="mt-6 pt-4 border-t border-gray-100">
        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-600">Total Revenue</span>
          <span className="font-semibold text-gray-900">$54,100</span>
        </div>
        <div className="flex items-center justify-between text-sm mt-1">
          <span className="text-gray-600">Total Bookings</span>
          <span className="font-semibold text-gray-900">164</span>
        </div>
      </div>
    </div>
  )
} 