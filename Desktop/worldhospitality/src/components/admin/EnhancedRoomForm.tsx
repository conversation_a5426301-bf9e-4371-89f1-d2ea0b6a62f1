'use client'

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { Room } from '@/types'
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog"
import { Star, StarOff, X, Upload, MapPin, Plus } from 'lucide-react'
import { HOTEL_AMENITIES, AMENITY_CATEGORIES, CURRENCY_RATES, convertPrice } from '@/lib/amenities'
import { Checkbox } from '@/components/ui/checkbox'
import { GoogleMapsPicker } from './GoogleMapsPicker'

const formSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  internalName: z.string().min(1, 'Internal name is required'),
  price: z.coerce.number().min(1, 'Price must be a positive number'),
  priceCurrency: z.enum(['SAR', 'USD', 'EUR', 'GBP']),
  description: z.string().min(1, 'Description is required'),
  amenities: z.array(z.string()).min(1, 'At least one amenity is required'),
  capacity: z.coerce.number().min(1, 'Capacity must be at least 1'),
  
  // Enhanced fields
  rating: z.coerce.number().min(1).max(5, 'Rating must be between 1 and 5'),
  reviewCount: z.coerce.number().min(0, 'Review count must be 0 or more'),
  roomType: z.enum(['standard', 'deluxe', 'suite', 'family']),
  distanceToHaram: z.coerce.number().min(1, 'Distance must be positive'),
  size: z.coerce.number().min(1, 'Size must be positive'),
  bedType: z.string().min(1, 'Bed type is required'),
  bedCount: z.coerce.number().min(1, 'Must have at least 1 bed').max(10, 'Maximum 10 beds'),
  maxGuests: z.coerce.number().min(1, 'Must accommodate at least 1 guest').max(20, 'Maximum 20 guests'),
  bathroomCount: z.coerce.number().min(1, 'Must have at least 1 bathroom'),
  hasBalcony: z.boolean(),
  hasKitchen: z.boolean(),
  hasBathroom: z.boolean(),
  
  // Simple location field  
  googleMapsLink: z.string().url().optional().or(z.literal('')),
  
  images: z.any().optional(),
})

interface EnhancedRoomFormProps {
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
  room?: Room | null;
  onSave: (data: z.infer<typeof formSchema>, imageFiles?: FileList, mainImageIndex?: number) => void;
}

export function EnhancedRoomForm({ isOpen, setIsOpen, room, onSave }: EnhancedRoomFormProps) {
  const [selectedAmenities, setSelectedAmenities] = useState<string[]>([])
  const [selectedImages, setSelectedImages] = useState<File[]>([])
  const [imagePreviewUrls, setImagePreviewUrls] = useState<string[]>([])
  const [mainImageIndex, setMainImageIndex] = useState<number>(0)
  const [priceInUSD, setPriceInUSD] = useState<number>(0)
  const [existingImages, setExistingImages] = useState<string[]>([])
  const [allDisplayImages, setAllDisplayImages] = useState<Array<{url: string, isExisting: boolean, file?: File}>>([])

  const { register, handleSubmit, formState: { errors }, setValue, watch, reset } = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: room?.name || '',
      internalName: (room as any)?.internal_name || room?.internalName || '',
      price: room?.price || 0,
      priceCurrency: room?.priceCurrency || 'SAR',
      description: room?.description || '',
      amenities: room?.amenities || [],
      capacity: room?.capacity || 2,
      rating: room?.rating || 4.0,
      reviewCount: room?.reviewCount || 0,
      roomType: room?.roomType || 'standard',
      distanceToHaram: room?.distanceToHaram || 500,
      size: room?.size || 25,
      bedType: room?.bedType || 'Double',
      bedCount: room?.bedCount || 1,
      maxGuests: room?.maxGuests || 2,
      bathroomCount: room?.bathroomCount || 1,
      hasBalcony: room?.hasBalcony || false,
      hasKitchen: room?.hasKitchen || false,
      hasBathroom: room?.hasBathroom !== false,
      googleMapsLink: room?.googleMapsLink || '',
    },
  })

  const watchedPrice = watch('price')
  const watchedCurrency = watch('priceCurrency')

  // Update USD price when price or currency changes
  useEffect(() => {
    if (watchedPrice && watchedCurrency) {
      const usdPrice = convertPrice(watchedPrice, watchedCurrency, 'USD')
      setPriceInUSD(usdPrice)
    }
  }, [watchedPrice, watchedCurrency])

  // Initialize form with existing room data when editing
  useEffect(() => {
    console.log('Room data in form:', room) // Debug log
    if (room) {
      console.log('Room images:', room.images) // Debug log
      
      // Reset form with all existing room data
      reset({
        name: room?.name || '',
        internalName: (room as any)?.internal_name || room?.internalName || '',
        price: room?.price || 0,
        priceCurrency: room?.priceCurrency || 'SAR',
        description: room?.description || '',
        amenities: room?.amenities || [],
        capacity: room?.capacity || 2,
        rating: room?.rating || 4.0,
        reviewCount: room?.reviewCount || 0,
        roomType: room?.roomType || 'standard',
        distanceToHaram: room?.distanceToHaram || 500,
        size: room?.size || 25,
        bedType: room?.bedType || 'Double',
        bedCount: room?.bedCount || 1,
        maxGuests: room?.maxGuests || 2,
        bathroomCount: room?.bathroomCount || 1,
        hasBalcony: room?.hasBalcony || false,
        hasKitchen: room?.hasKitchen || false,
        hasBathroom: room?.hasBathroom !== false,
        googleMapsLink: room?.googleMapsLink || '',
      })
      
      // Set amenities state
      if (room.amenities && Array.isArray(room.amenities)) {
        setSelectedAmenities(room.amenities)
      }
      
      // Set main image index
      if (room.mainImageIndex !== undefined) {
        setMainImageIndex(room.mainImageIndex)
      }
      
      // Load existing images
      if (room.images && Array.isArray(room.images) && room.images.length > 0) {
        console.log('Setting existing images:', room.images) // Debug log
        console.log('Full image URLs:')
        room.images.forEach((url, i) => {
          console.log(`  ${i}: "${url}"`)
          console.log(`  Length: ${url.length}`)
          console.log(`  Starts with https: ${url.startsWith('https')}`)
        })
        
        setExistingImages(room.images)
        const existingImageObjects = room.images.map((url, index) => {
          console.log(`Image ${index}:`, url) // Debug each image URL
          return {
            url,
            isExisting: true
          }
        })
        
        // Add a test image to see if the display works at all
        existingImageObjects.push({
          url: 'https://images.unsplash.com/photo-1566665797739-1674de7a421a?q=80&w=200&auto=format&fit=crop',
          isExisting: false
        })
        
        setAllDisplayImages(existingImageObjects)
      } else {
        console.log('No existing images found') // Debug log
        setExistingImages([])
        setAllDisplayImages([])
      }
    } else {
      console.log('No room data - setting up new room form') // Debug log
      // Reset to empty form for new room
      reset({
        name: '',
        internalName: '',
        price: 0,
        priceCurrency: 'SAR',
        description: '',
        amenities: [],
        capacity: 2,
        rating: 4.0,
        reviewCount: 0,
        roomType: 'standard',
        distanceToHaram: 500,
        size: 25,
        bedType: 'Double',
        bedCount: 1,
        maxGuests: 2,
        bathroomCount: 1,
        hasBalcony: false,
        hasKitchen: false,
        hasBathroom: true,
        googleMapsLink: '',
      })
      setSelectedAmenities([])
      setMainImageIndex(0)
      setExistingImages([])
      setAllDisplayImages([])
    }
  }, [room, reset])

  // Update combined display images when new images are added
  useEffect(() => {
    const newImageObjects = selectedImages.map(file => ({
      url: URL.createObjectURL(file),
      isExisting: false,
      file
    }))
    
    // Combine existing images with new images
    const combined = [...existingImages.map(url => ({ url, isExisting: true })), ...newImageObjects]
    setAllDisplayImages(combined)

    // Cleanup blob URLs on unmount
    return () => {
      newImageObjects.forEach(img => {
        if (img.url.startsWith('blob:')) {
          URL.revokeObjectURL(img.url)
        }
      })
    }
  }, [selectedImages, existingImages])

  const handleAmenityChange = (amenityId: string, checked: boolean) => {
    const newAmenities = checked 
      ? [...selectedAmenities, amenityId]
      : selectedAmenities.filter(id => id !== amenityId)
    
    setSelectedAmenities(newAmenities)
    setValue('amenities', newAmenities)
  }

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const filesArray = Array.from(e.target.files)
      setSelectedImages(prev => [...prev, ...filesArray])
    }
  }

  const removeImage = (index: number) => {
    // Revoke the URL for the removed image
    if (imagePreviewUrls[index] && imagePreviewUrls[index].startsWith('blob:')) {
      URL.revokeObjectURL(imagePreviewUrls[index])
    }
    
    setSelectedImages(prev => prev.filter((_, i) => i !== index))
    setImagePreviewUrls(prev => prev.filter((_, i) => i !== index))
    
    // Adjust main image index if needed
    if (index === mainImageIndex) {
      setMainImageIndex(0) // Set first image as main if current main is removed
    } else if (index < mainImageIndex) {
      setMainImageIndex(mainImageIndex - 1)
    }
  }

  const onSubmit = (data: z.infer<typeof formSchema>) => {
    const imageFiles = selectedImages.length > 0 ? 
      Object.assign(selectedImages, { length: selectedImages.length }) as FileList : 
      undefined
    onSave(data, imageFiles, mainImageIndex)
    
    // Clear form state after saving
    reset()
    setSelectedAmenities([])
    setSelectedImages([])
    setMainImageIndex(0)
    setExistingImages([])
    setAllDisplayImages([])
  }

  // Clear form when modal closes
  const handleModalClose = () => {
    setIsOpen(false)
    reset()
    setSelectedAmenities([])
    setSelectedImages([])
    setMainImageIndex(0)
    setExistingImages([])
    setAllDisplayImages([])
  }

  const groupedAmenities = AMENITY_CATEGORIES.reduce((acc, category) => {
    acc[category.id] = HOTEL_AMENITIES.filter(amenity => amenity.category === category.id)
    return acc
  }, {} as Record<string, typeof HOTEL_AMENITIES>)

  return (
    <Dialog open={isOpen} onOpenChange={handleModalClose}>
      <DialogContent className="max-w-6xl max-h-[95vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-2xl">{room ? 'Edit Room' : 'Add New Room'}</DialogTitle>
        </DialogHeader>
        
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
          {/* Basic Information */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="lg:col-span-2">
              <h3 className="text-lg font-semibold mb-4 text-blue-600">Basic Information</h3>
            </div>
            
            <div>
              <Label htmlFor="name">Room Name *</Label>
              <Input id="name" {...register('name')} placeholder="e.g., Deluxe Suite with Kaaba View" />
              {errors.name && <p className="text-red-500 text-xs mt-1">{errors.name.message}</p>}
            </div>
            
            <div>
              <Label htmlFor="internalName">Internal Room Name *</Label>
              <Input id="internalName" {...register('internalName')} placeholder="e.g., Deluxe_Suite_Kaaba_View" />
              {errors.internalName && <p className="text-red-500 text-xs mt-1">{errors.internalName.message}</p>}
            </div>

            <div>
              <Label htmlFor="roomType">Room Type *</Label>
              <select 
                id="roomType" 
                {...register('roomType')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="standard">Standard Room</option>
                <option value="deluxe">Deluxe Room</option>
                <option value="suite">Suite</option>
                <option value="family">Family Room</option>
              </select>
              {errors.roomType && <p className="text-red-500 text-xs mt-1">{errors.roomType.message}</p>}
            </div>

            <div className="lg:col-span-2">
              <Label htmlFor="description">Description *</Label>
              <Textarea 
                id="description" 
                {...register('description')} 
                placeholder="Describe the room features, location, and what makes it special..."
                rows={4}
              />
              {errors.description && <p className="text-red-500 text-xs mt-1">{errors.description.message}</p>}
            </div>
          </div>

          {/* Pricing */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-3">
              <h3 className="text-lg font-semibold mb-4 text-green-600">Pricing</h3>
            </div>
            
            <div>
              <Label htmlFor="priceCurrency">Currency *</Label>
              <select 
                id="priceCurrency" 
                {...register('priceCurrency')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {Object.entries(CURRENCY_RATES).map(([code, info]) => (
                  <option key={code} value={code}>
                    {info.symbol} {info.name} ({code})
                  </option>
                ))}
              </select>
            </div>
            
            <div>
              <Label htmlFor="price">Price per Night *</Label>
              <div className="relative">
                <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                  {CURRENCY_RATES[watchedCurrency]?.symbol}
                </span>
                <Input 
                  id="price" 
                  type="number" 
                  {...register('price')} 
                  placeholder="450"
                  className="pl-8"
                />
              </div>
              {errors.price && <p className="text-red-500 text-xs mt-1">{errors.price.message}</p>}
            </div>

            <div>
              <Label>USD Equivalent</Label>
              <div className="px-3 py-2 bg-gray-50 border border-gray-200 rounded-md">
                <span className="text-gray-700">${priceInUSD} USD</span>
              </div>
            </div>
          </div>

          {/* Room Details */}
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
            <div className="lg:col-span-4">
              <h3 className="text-lg font-semibold mb-4 text-purple-600">Room Details</h3>
            </div>
            
            <div>
              <Label htmlFor="capacity">Room Capacity *</Label>
              <Input id="capacity" type="number" min="1" max="10" {...register('capacity')} />
              <span className="text-xs text-gray-500">Base capacity for booking</span>
              {errors.capacity && <p className="text-red-500 text-xs mt-1">{errors.capacity.message}</p>}
            </div>

            <div>
              <Label htmlFor="maxGuests">Max Guests *</Label>
              <Input id="maxGuests" type="number" min="1" max="20" {...register('maxGuests')} />
              <span className="text-xs text-gray-500">Maximum guests allowed</span>
              {errors.maxGuests && <p className="text-red-500 text-xs mt-1">{errors.maxGuests.message}</p>}
            </div>
            
            <div>
              <Label htmlFor="size">Size (sqm) *</Label>
              <Input id="size" type="number" min="1" {...register('size')} placeholder="35" />
              {errors.size && <p className="text-red-500 text-xs mt-1">{errors.size.message}</p>}
            </div>
            
            <div>
              <Label htmlFor="distanceToHaram">Distance to Haram (m) *</Label>
              <Input id="distanceToHaram" type="number" min="1" {...register('distanceToHaram')} placeholder="150" />
              {errors.distanceToHaram && <p className="text-red-500 text-xs mt-1">{errors.distanceToHaram.message}</p>}
            </div>
          </div>

          {/* Bed & Bathroom Details */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-3">
              <h3 className="text-lg font-semibold mb-4 text-orange-600">Bed & Bathroom Details</h3>
            </div>
            
            <div>
              <Label htmlFor="bedType">Bed Type *</Label>
              <select 
                id="bedType" 
                {...register('bedType')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="Single">Single Bed</option>
                <option value="Twin">Twin Beds</option>
                <option value="Double">Double Bed</option>
                <option value="Queen">Queen Bed</option>
                <option value="King">King Bed</option>
                <option value="2 Queens">2 Queen Beds</option>
                <option value="King & Single">King + Single Bed</option>
                <option value="Custom">Custom Configuration</option>
              </select>
              {errors.bedType && <p className="text-red-500 text-xs mt-1">{errors.bedType.message}</p>}
            </div>

            <div>
              <Label htmlFor="bedCount">Number of Beds *</Label>
              <Input id="bedCount" type="number" min="1" max="10" {...register('bedCount')} />
              <span className="text-xs text-gray-500">Total beds in room</span>
              {errors.bedCount && <p className="text-red-500 text-xs mt-1">{errors.bedCount.message}</p>}
            </div>

            <div>
              <Label htmlFor="bathroomCount">Number of Bathrooms *</Label>
              <Input id="bathroomCount" type="number" min="1" max="5" {...register('bathroomCount')} />
              <span className="text-xs text-gray-500">Private bathrooms</span>
              {errors.bathroomCount && <p className="text-red-500 text-xs mt-1">{errors.bathroomCount.message}</p>}
            </div>
          </div>

          {/* Rating & Reviews */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="lg:col-span-2">
              <h3 className="text-lg font-semibold mb-4 text-yellow-600">Rating & Reviews</h3>
            </div>
            
            <div>
              <Label htmlFor="rating">Rating (1-5) *</Label>
              <Input 
                id="rating" 
                type="number" 
                min="1" 
                max="5" 
                step="0.1" 
                {...register('rating')} 
                placeholder="4.5" 
              />
              {errors.rating && <p className="text-red-500 text-xs mt-1">{errors.rating.message}</p>}
            </div>
            
            <div>
              <Label htmlFor="reviewCount">Number of Reviews *</Label>
              <Input id="reviewCount" type="number" min="0" {...register('reviewCount')} placeholder="127" />
              {errors.reviewCount && <p className="text-red-500 text-xs mt-1">{errors.reviewCount.message}</p>}
            </div>
          </div>

          {/* Amenities */}
          <div>
            <h3 className="text-lg font-semibold mb-4 text-indigo-600">
              Amenities * 
              <span className="text-sm font-normal text-gray-500">
                ({selectedAmenities.length} selected)
              </span>
            </h3>
            
            <div className="space-y-6">
              {AMENITY_CATEGORIES.map((category) => (
                <div key={category.id} className="border rounded-lg p-4">
                  <h4 className={`font-medium mb-3 px-3 py-1 rounded-full inline-block text-sm ${category.color}`}>
                    {category.label}
                  </h4>
                  <div className="grid grid-cols-2 lg:grid-cols-3 gap-3">
                    {groupedAmenities[category.id]?.map((amenity) => (
                      <label key={amenity.id} className="flex items-center space-x-2 cursor-pointer hover:bg-gray-50 p-2 rounded">
                        <Checkbox
                          checked={selectedAmenities.includes(amenity.id)}
                          onCheckedChange={(checked) => handleAmenityChange(amenity.id, checked as boolean)}
                        />
                        <span className="text-sm">{amenity.label}</span>
                      </label>
                    ))}
                  </div>
                </div>
              ))}
            </div>
            {errors.amenities && <p className="text-red-500 text-xs mt-1">{errors.amenities.message}</p>}
          </div>

          {/* Room Features */}
          <div>
            <h3 className="text-lg font-semibold mb-4 text-teal-600">Additional Features</h3>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
              <div className="flex items-center space-x-2">
                <input 
                  type="checkbox" 
                  id="hasBalcony" 
                  {...register('hasBalcony')}
                  className="w-4 h-4 text-blue-600 rounded focus:ring-blue-500"
                />
                <Label htmlFor="hasBalcony">Has Balcony/Terrace</Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <input 
                  type="checkbox" 
                  id="hasKitchen" 
                  {...register('hasKitchen')}
                  className="w-4 h-4 text-blue-600 rounded focus:ring-blue-500"
                />
                <Label htmlFor="hasKitchen">Has Kitchen/Kitchenette</Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <input 
                  type="checkbox" 
                  id="hasBathroom" 
                  {...register('hasBathroom')}
                  className="w-4 h-4 text-blue-600 rounded focus:ring-blue-500"
                />
                <Label htmlFor="hasBathroom">Has Private Bathroom</Label>
              </div>
            </div>
          </div>

          {/* Location - Google Maps Link */}
          <div>
            <h3 className="text-lg font-semibold mb-4 text-purple-600">📍 Location</h3>
            <div>
              <Label htmlFor="googleMapsLink">Google Maps Link</Label>
              <Input 
                id="googleMapsLink" 
                {...register('googleMapsLink')} 
                placeholder="Paste Google Maps link here (e.g., https://maps.google.com/...)"
              />
              <span className="text-xs text-gray-500">
                Get this by sharing a location from Google Maps
              </span>
              {errors.googleMapsLink && <p className="text-red-500 text-xs mt-1">{errors.googleMapsLink.message}</p>}
            </div>
          </div>

          {/* Bulk Image Upload */}
          <div>
            <h3 className="text-lg font-semibold mb-4 text-rose-600">Room Images</h3>
            
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6">
              <div className="text-center">
                <Upload className="mx-auto h-12 w-12 text-gray-400" />
                <div className="mt-4">
                  <label htmlFor="images" className="cursor-pointer">
                    <span className="mt-2 block text-sm font-medium text-gray-900">
                      Upload multiple room images
                    </span>
                    <span className="text-xs text-gray-500">
                      PNG, JPG, WEBP up to 10MB each. Select multiple files.
                    </span>
                  </label>
                  <Input
                    id="images"
                    type="file"
                    multiple
                    accept="image/*"
                    onChange={handleImageChange}
                    className="hidden"
                  />
                </div>
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={() => document.getElementById('images')?.click()}
                  className="mt-4"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Select Images
                </Button>
              </div>
            </div>

            {/* Selected Images Preview with Main Photo Selection */}
            {allDisplayImages.length > 0 && (
              <div className="mt-4">
                <h4 className="font-medium mb-2">
                  Room Images ({allDisplayImages.length})
                  <span className="text-sm text-gray-500 ml-2">
                    Click ⭐ to set as main photo (existing or new images)
                  </span>
                </h4>
                <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
                  {allDisplayImages.map((img, index) => (
                    <div key={index} className="relative group">
                       {/* Main Image Preview */}
                       <img
                         src={img.url}
                         alt={`Preview ${index + 1}`}
                         className="w-full h-24 object-cover rounded-lg"
                         onError={(e) => {
                           console.error('Image failed to load:', img.url)
                           const target = e.target as HTMLImageElement
                           target.style.backgroundColor = '#ef4444'
                           target.style.color = 'white'
                           target.style.display = 'flex'
                           target.style.alignItems = 'center'
                           target.style.justifyContent = 'center'
                           target.innerHTML = '<span style="font-size: 12px;">Failed</span>'
                         }}
                         style={{
                           border: index === mainImageIndex ? '3px solid #fbbf24' : '2px solid #e5e7eb',
                           opacity: 1,
                           visibility: 'visible',
                           display: 'block'
                         }}
                       />

                       {/* Main Photo Star Icon */}
                       <button
                         type="button"
                         onClick={() => setMainImageIndex(index)}
                         className={`absolute top-2 left-2 p-1 rounded-full transition-all ${
                           index === mainImageIndex 
                             ? 'bg-yellow-500 text-white' 
                             : 'bg-black bg-opacity-50 text-yellow-400 hover:bg-yellow-500 hover:text-white'
                         }`}
                         title={index === mainImageIndex ? 'Main Photo' : 'Set as Main Photo'}
                       >
                         <svg
                           className="w-4 h-4"
                           fill="currentColor"
                           viewBox="0 0 20 20"
                         >
                           <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                         </svg>
                       </button>

                       {/* Main Photo Badge */}
                       {index === mainImageIndex && (
                         <div className="absolute top-2 right-2 bg-yellow-500 text-white text-xs px-2 py-1 rounded-full font-bold">
                           MAIN
                         </div>
                       )}

                       {/* Remove Button (only for new images) */}
                       {!img.isExisting && (
                         <button
                           type="button"
                           onClick={() => removeImage(index)}
                           className="absolute top-2 right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm hover:bg-red-600 transition-colors"
                         >
                           ×
                         </button>
                       )}

                       {/* Image Type Badge */}
                       <div className="absolute bottom-2 left-2">
                         {img.isExisting ? (
                           <span className="bg-blue-500 text-white text-xs px-2 py-1 rounded">
                             Existing
                           </span>
                         ) : (
                           <span className="bg-green-500 text-white text-xs px-2 py-1 rounded">
                             New
                           </span>
                         )}
                       </div>
                    </div>
                  ))}
                </div>
                
                {/* Main Photo Info */}
                {allDisplayImages.length > 0 && (
                  <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mt-4">
                    <div className="flex items-center gap-2">
                      <Star className="w-4 h-4 text-yellow-600 fill-current" />
                      <span className="text-sm font-medium text-yellow-800">
                        Main Photo: {allDisplayImages[mainImageIndex]?.file?.name || `Image ${mainImageIndex + 1}`}
                      </span>
                    </div>
                    <p className="text-xs text-yellow-700 mt-1">
                      This will be the primary image shown to guests on listings and search results.
                    </p>
                  </div>
                )}
              </div>
            )}
          </div>

          <DialogFooter className="gap-3 pt-6 border-t">
            <Button type="button" variant="outline" onClick={() => setIsOpen(false)}>
              Cancel
            </Button>
            <Button type="submit" className="bg-blue-600 hover:bg-blue-700 min-w-[120px]">
              {room ? 'Update Room' : 'Add Room'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
} 