'use client'

import { useState, useEffect, useRef } from 'react'
import { useRouter } from 'next/navigation'
import { Search, MapPin } from 'lucide-react'
import { Loader } from '@googlemaps/js-api-loader'
import { DatePicker } from './ui/DatePicker'

interface SearchBarProps {
  lng: string
  translations: {
    where: string
    where_placeholder: string
    checkin: string
    checkout: string
    who: string
    guest: string
    guests: string
  }
  isRTL: boolean
}

export const SearchBar = ({ lng, translations, isRTL }: SearchBarProps) => {
  const router = useRouter()
  const [searchData, setSearchData] = useState({
    location: '',
    checkIn: null as Date | null,
    checkOut: null as Date | null,
    guests: 1
  })
  
  const [suggestions, setSuggestions] = useState<google.maps.places.AutocompletePrediction[]>([])
  const [showSuggestions, setShowSuggestions] = useState(false)
  const [isLoadingPlaces, setIsLoadingPlaces] = useState(false)
  const locationInputRef = useRef<HTMLInputElement>(null)
  const suggestionsRef = useRef<HTMLDivElement>(null)

  const handleInputChange = (field: string, value: string | number | Date | null) => {
    setSearchData(prev => ({
      ...prev,
      [field]: value
    }))
    
    if (field === 'location' && typeof value === 'string') {
      setShowSuggestions(true)
      if (value.length >= 2) {
        fetchCitySuggestions(value)
      } else {
        setSuggestions([])
      }
    }
  }

  const handleSearch = (e?: React.FormEvent) => {
    if (e) e.preventDefault()
    setShowSuggestions(false)
    
    // Create search parameters
    const searchParams = new URLSearchParams()
    if (searchData.location) searchParams.set('location', searchData.location)
    if (searchData.checkIn) searchParams.set('checkin', searchData.checkIn.toISOString().split('T')[0])
    if (searchData.checkOut) searchParams.set('checkout', searchData.checkOut.toISOString().split('T')[0])
    if (searchData.guests > 1) searchParams.set('guests', searchData.guests.toString())

    // Navigate to rooms page with search parameters
    router.push(`/${lng}/rooms?${searchParams.toString()}`)
  }

  const handleDateChange = (field: 'checkIn' | 'checkOut', date: Date) => {
    handleInputChange(field, date)
  }

  const fetchCitySuggestions = async (input: string) => {
    if (typeof window === 'undefined') return
    
    try {
      setIsLoadingPlaces(true)
      
      // Load Google Maps API if not already loaded
      if (!(window as any).google?.maps?.places) {
        const loader = new Loader({
          apiKey: process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY || '',
          libraries: ['places']
        })
        await loader.load()
      }
      
      const service = new (window as any).google.maps.places.AutocompleteService()
      service.getPlacePredictions(
        {
          input,
          types: ['(cities)']
        },
        (predictions: google.maps.places.AutocompletePrediction[] | null) => {
          setSuggestions(predictions || [])
          setIsLoadingPlaces(false)
        }
      )
    } catch (error) {
      console.error('Error fetching city suggestions:', error)
      setIsLoadingPlaces(false)
    }
  }

  const handleSuggestionClick = (suggestion: google.maps.places.AutocompletePrediction) => {
    setSearchData(prev => ({ ...prev, location: suggestion.description }))
    setShowSuggestions(false)
    setSuggestions([])
  }

  // Close suggestions when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        suggestionsRef.current &&
        !suggestionsRef.current.contains(event.target as Node) &&
        locationInputRef.current &&
        !locationInputRef.current.contains(event.target as Node)
      ) {
        setShowSuggestions(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  return (
    <form onSubmit={handleSearch} className="max-w-4xl mx-auto">
      <div className="bg-white rounded-full shadow-lg border border-gray-300 p-2">
        <div className={`grid grid-cols-1 md:grid-cols-4 gap-0 ${isRTL ? 'direction-rtl' : ''}`}>
          {/* Where */}
          <div className="relative p-4 hover:bg-gray-50 rounded-full cursor-pointer transition-colors">
            <div className={`${isRTL ? 'text-right' : 'text-left'}`}>
              <label className="text-xs font-semibold text-gray-900 mb-1 block">
                {translations.where}
              </label>
              <input
                ref={locationInputRef}
                type="text"
                value={searchData.location}
                onChange={(e) => handleInputChange('location', e.target.value)}
                onFocus={() => setShowSuggestions(true)}
                placeholder={translations.where_placeholder}
                className="text-sm text-gray-500 bg-transparent border-none outline-none w-full placeholder-gray-400"
                autoComplete="off"
              />
            </div>
            
            {/* City Suggestions Dropdown */}
            {showSuggestions && (suggestions.length > 0 || isLoadingPlaces) && (
              <div 
                ref={suggestionsRef}
                className="absolute top-full left-0 right-0 bg-white border border-gray-200 rounded-lg shadow-lg z-50 mt-1 max-h-60 overflow-y-auto"
              >
                {isLoadingPlaces && (
                  <div className="p-3 text-sm text-gray-500 text-center">
                    Loading suggestions...
                  </div>
                )}
                {suggestions.map((suggestion) => (
                  <button
                    key={suggestion.place_id}
                    type="button"
                    onClick={() => handleSuggestionClick(suggestion)}
                    className="w-full text-left p-3 hover:bg-gray-50 transition-colors border-b border-gray-100 last:border-b-0 flex items-center gap-2"
                  >
                    <MapPin className="h-4 w-4 text-gray-400 flex-shrink-0" />
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {suggestion.structured_formatting.main_text}
                      </div>
                      <div className="text-xs text-gray-500">
                        {suggestion.structured_formatting.secondary_text}
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* Check in */}
          <div className="p-4 hover:bg-gray-50 rounded-full cursor-pointer transition-colors border-l border-gray-200 hidden md:block">
            <div className={`${isRTL ? 'text-right' : 'text-left'}`}>
              <DatePicker
                selected={searchData.checkIn || undefined}
                onSelect={(date) => handleDateChange('checkIn', date)}
                placeholder="Add dates"
                minDate={new Date()}
                maxDate={searchData.checkOut || undefined}
                label={translations.checkin}
              />
            </div>
          </div>

          {/* Check out */}
          <div className="p-4 hover:bg-gray-50 rounded-full cursor-pointer transition-colors border-l border-gray-200 hidden md:block">
            <div className={`${isRTL ? 'text-right' : 'text-left'}`}>
              <DatePicker
                selected={searchData.checkOut || undefined}
                onSelect={(date) => handleDateChange('checkOut', date)}
                placeholder="Add dates"
                minDate={searchData.checkIn || new Date()}
                label={translations.checkout}
              />
            </div>
          </div>

          {/* Who + Search */}
          <div className="flex items-center">
            <div className="flex-1 p-4 hover:bg-gray-50 rounded-full cursor-pointer transition-colors border-l border-gray-200">
              <div className={`${isRTL ? 'text-right' : 'text-left'}`}>
                <label className="text-xs font-semibold text-gray-900 mb-1 block">
                  {translations.who}
                </label>
                <div className="flex items-center gap-3">
                  <button
                    type="button"
                    onClick={() => handleInputChange('guests', Math.max(1, searchData.guests - 1))}
                    disabled={searchData.guests <= 1}
                    className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed hover:border-gray-900"
                  >
                    −
                  </button>
                  <span className="w-8 text-center text-sm text-gray-900">
                    {searchData.guests} {searchData.guests === 1 ? translations.guest : translations.guests}
                  </span>
                  <button
                    type="button"
                    onClick={() => handleInputChange('guests', Math.min(12, searchData.guests + 1))}
                    disabled={searchData.guests >= 12}
                    className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed hover:border-gray-900"
                  >
                    +
                  </button>
                </div>
              </div>
            </div>
            <button
              type="submit"
              className={`bg-blue-600 hover:bg-blue-700 text-white p-4 rounded-full transition-colors ${isRTL ? 'mr-2' : 'ml-2'}`}
            >
              <Search className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>
    </form>
  )
} 