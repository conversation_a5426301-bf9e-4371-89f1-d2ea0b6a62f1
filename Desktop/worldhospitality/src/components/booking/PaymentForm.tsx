'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { CreditCard, Lock, MapPin, User } from 'lucide-react'

interface PaymentFormProps {
  lng: string
  isRTL: boolean
  bookingParams: {
    roomId?: string
    checkin?: string
    checkout?: string
    guests?: string
  }
}

export const PaymentForm = ({ lng, isRTL, bookingParams }: PaymentFormProps) => {
  const router = useRouter()
  const [paymentData, setPaymentData] = useState({
    paymentMethod: 'card',
    cardNumber: '',
    expiryDate: '',
    cvv: '',
    cardName: '',
    billingAddress: {
      firstName: '',
      lastName: '',
      address: '',
      city: '',
      country: 'Saudi Arabia',
      postalCode: ''
    }
  })
  const [isLoading, setIsLoading] = useState(false)

  const handleInputChange = (field: string, value: string) => {
    if (field.startsWith('billing.')) {
      const billingField = field.replace('billing.', '')
      setPaymentData(prev => ({
        ...prev,
        billingAddress: {
          ...prev.billingAddress,
          [billingField]: value
        }
      }))
    } else {
      setPaymentData(prev => ({
        ...prev,
        [field]: value
      }))
    }
  }

  const formatCardNumber = (value: string) => {
    const cleaned = value.replace(/\s/g, '')
    const match = cleaned.match(/.{1,4}/g)
    return match ? match.join(' ') : ''
  }

  const formatExpiryDate = (value: string) => {
    const cleaned = value.replace(/\D/g, '')
    if (cleaned.length >= 2) {
      return cleaned.slice(0, 2) + '/' + cleaned.slice(2, 4)
    }
    return cleaned
  }

  const handleCardNumberChange = (value: string) => {
    const formatted = formatCardNumber(value.replace(/\s/g, ''))
    if (formatted.replace(/\s/g, '').length <= 16) {
      handleInputChange('cardNumber', formatted)
    }
  }

  const handleExpiryChange = (value: string) => {
    const formatted = formatExpiryDate(value)
    if (formatted.length <= 5) {
      handleInputChange('expiryDate', formatted)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    // Simulate payment processing
    await new Promise(resolve => setTimeout(resolve, 2000))

    // Build the next page URL with booking params
    const params = new URLSearchParams()
    if (bookingParams.roomId) params.set('roomId', bookingParams.roomId)
    if (bookingParams.checkin) params.set('checkin', bookingParams.checkin)
    if (bookingParams.checkout) params.set('checkout', bookingParams.checkout)
    if (bookingParams.guests) params.set('guests', bookingParams.guests)

    // Navigate to review page
    router.push(`/${lng}/book/review?${params.toString()}`)
  }

  return (
    <div className="bg-white rounded-xl shadow-lg p-6">
      <h2 className="text-xl font-semibold text-gray-900 mb-6">Payment details</h2>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Payment Method Selection */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 mb-4">Choose payment method</h3>
          <div className="space-y-3">
            <label className="flex items-center p-4 border-2 border-blue-200 bg-blue-50 rounded-lg cursor-pointer">
              <input
                type="radio"
                name="paymentMethod"
                value="card"
                checked={paymentData.paymentMethod === 'card'}
                onChange={(e) => handleInputChange('paymentMethod', e.target.value)}
                className="h-4 w-4 text-blue-600"
              />
              <CreditCard className="h-5 w-5 ml-3 text-blue-600" />
              <span className="ml-3 text-sm font-medium text-gray-900">Credit or debit card</span>
              <div className="ml-auto flex space-x-2">
                <div className="w-8 h-5 bg-blue-600 rounded text-white text-xs flex items-center justify-center font-bold">VISA</div>
                <div className="w-8 h-5 bg-red-600 rounded text-white text-xs flex items-center justify-center font-bold">MC</div>
              </div>
            </label>
            
            <label className="flex items-center p-4 border-2 border-gray-200 rounded-lg cursor-pointer opacity-50">
              <input
                type="radio"
                name="paymentMethod"
                value="paypal"
                disabled
                className="h-4 w-4 text-blue-600"
              />
              <div className="h-5 w-5 ml-3 bg-blue-600 rounded text-white text-xs flex items-center justify-center font-bold">P</div>
              <span className="ml-3 text-sm font-medium text-gray-500">PayPal (Coming soon)</span>
            </label>
          </div>
        </div>

        {/* Card Details */}
        {paymentData.paymentMethod === 'card' && (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Card number
              </label>
              <div className="relative">
                <input
                  type="text"
                  required
                  value={paymentData.cardNumber}
                  onChange={(e) => handleCardNumberChange(e.target.value)}
                  placeholder="1234 5678 9012 3456"
                  className="w-full pl-4 pr-12 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <CreditCard className="absolute right-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Expiry date
                </label>
                <input
                  type="text"
                  required
                  value={paymentData.expiryDate}
                  onChange={(e) => handleExpiryChange(e.target.value)}
                  placeholder="MM/YY"
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  CVV
                </label>
                <input
                  type="text"
                  required
                  value={paymentData.cvv}
                  onChange={(e) => handleInputChange('cvv', e.target.value.replace(/\D/g, '').slice(0, 4))}
                  placeholder="123"
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Name on card
              </label>
              <input
                type="text"
                required
                value={paymentData.cardName}
                onChange={(e) => handleInputChange('cardName', e.target.value)}
                placeholder="Full name as shown on card"
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>
        )}

        {/* Billing Address */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
            <MapPin className="h-5 w-5 mr-2" />
            Billing address
          </h3>
          
          <div className="grid grid-cols-2 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                First name
              </label>
              <input
                type="text"
                required
                value={paymentData.billingAddress.firstName}
                onChange={(e) => handleInputChange('billing.firstName', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Last name
              </label>
              <input
                type="text"
                required
                value={paymentData.billingAddress.lastName}
                onChange={(e) => handleInputChange('billing.lastName', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>

          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Address
            </label>
            <input
              type="text"
              required
              value={paymentData.billingAddress.address}
              onChange={(e) => handleInputChange('billing.address', e.target.value)}
              placeholder="Street address"
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                City
              </label>
              <input
                type="text"
                required
                value={paymentData.billingAddress.city}
                onChange={(e) => handleInputChange('billing.city', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Postal code
              </label>
              <input
                type="text"
                required
                value={paymentData.billingAddress.postalCode}
                onChange={(e) => handleInputChange('billing.postalCode', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>

          <div className="mt-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Country/Region
            </label>
            <select
              value={paymentData.billingAddress.country}
              onChange={(e) => handleInputChange('billing.country', e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="Saudi Arabia">Saudi Arabia</option>
              <option value="United Arab Emirates">United Arab Emirates</option>
              <option value="Kuwait">Kuwait</option>
              <option value="Qatar">Qatar</option>
              <option value="Bahrain">Bahrain</option>
              <option value="Oman">Oman</option>
            </select>
          </div>
        </div>

        {/* Security Notice */}
        <div className="bg-gray-50 rounded-lg p-4">
          <div className="flex items-start">
            <Lock className="h-5 w-5 text-green-600 mt-0.5 mr-3 flex-shrink-0" />
            <div className="text-sm text-gray-600">
              <p className="font-medium text-gray-900 mb-1">Your payment is secure</p>
              <p>We use SSL encryption to protect your personal and payment information. Your card details are never stored on our servers.</p>
            </div>
          </div>
        </div>

        {/* Submit Button */}
        <button
          type="submit"
          disabled={isLoading}
          className="w-full bg-gradient-to-r from-blue-500 to-blue-600 text-white py-4 px-6 rounded-lg font-semibold hover:from-blue-600 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isLoading ? (
            <div className="flex items-center justify-center">
              <div className="animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent mr-2"></div>
              Processing payment...
            </div>
          ) : (
            'Continue to review'
          )}
        </button>
      </form>
    </div>
  )
} 