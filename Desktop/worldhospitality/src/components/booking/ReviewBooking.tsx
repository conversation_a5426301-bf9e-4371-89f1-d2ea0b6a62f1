'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Room } from '@/types'
import { Check, MapPin, Calendar, Users, CreditCard, Shield, Clock } from 'lucide-react'

interface ReviewBookingProps {
  lng: string
  isRTL: boolean
  room: Room
  bookingDetails: {
    checkin: string
    checkout: string
    guests: number
    nights: number
    subtotal: number
    serviceFee: number
    taxes: number
    total: number
  }
}

export const ReviewBooking = ({ lng, isRTL, room, bookingDetails }: ReviewBookingProps) => {
  const router = useRouter()
  const [acceptedTerms, setAcceptedTerms] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)

  const checkInDate = new Date(bookingDetails.checkin)
  const checkOutDate = new Date(bookingDetails.checkout)

  const handleConfirmBooking = async () => {
    if (!acceptedTerms) {
      alert('Please accept the terms and conditions to continue.')
      return
    }

    setIsSubmitting(true)

    // Simulate booking confirmation
    await new Promise(resolve => setTimeout(resolve, 3000))

    // Generate a mock booking ID
    const bookingId = 'WH' + Date.now().toString().slice(-6)

    // Navigate to confirmation page
    router.push(`/${lng}/book/confirmation?bookingId=${bookingId}&roomId=${room.id}&checkin=${bookingDetails.checkin}&checkout=${bookingDetails.checkout}&guests=${bookingDetails.guests}`)
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
      {/* Main Content */}
      <div className="lg:col-span-2 space-y-8">
        {/* Trip Summary */}
        <div className="bg-white rounded-xl shadow-lg p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-6">Your trip</h2>
          
          <div className="flex gap-6 mb-6">
            <div className="w-32 h-24 bg-gray-200 rounded-lg overflow-hidden flex-shrink-0">
              <img 
                src={room.image} 
                alt={room.name}
                className="w-full h-full object-cover"
              />
            </div>
            <div className="flex-1">
              <h3 className="text-lg font-semibold text-gray-900 mb-1">{room.name}</h3>
              <p className="text-gray-600 mb-2">Near Holy Mosque, Makkah</p>
              <div className="flex items-center">
                <div className="flex items-center">
                  {[...Array(5)].map((_, i) => (
                    <svg key={i} className="w-4 h-4 text-yellow-400 fill-current" viewBox="0 0 20 20">
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                  ))}
                  <span className="text-sm text-gray-600 ml-1">4.9 (127 reviews)</span>
                </div>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center">
              <Calendar className="h-5 w-5 text-gray-500 mr-3" />
              <div>
                <div className="text-sm font-medium text-gray-900">Check-in</div>
                <div className="text-sm text-gray-600">{checkInDate.toLocaleDateString()}</div>
                <div className="text-xs text-gray-500">After 3:00 PM</div>
              </div>
            </div>
            
            <div className="flex items-center">
              <Calendar className="h-5 w-5 text-gray-500 mr-3" />
              <div>
                <div className="text-sm font-medium text-gray-900">Check-out</div>
                <div className="text-sm text-gray-600">{checkOutDate.toLocaleDateString()}</div>
                <div className="text-xs text-gray-500">Before 11:00 AM</div>
              </div>
            </div>
            
            <div className="flex items-center">
              <Users className="h-5 w-5 text-gray-500 mr-3" />
              <div>
                <div className="text-sm font-medium text-gray-900">Guests</div>
                <div className="text-sm text-gray-600">{bookingDetails.guests} guest{bookingDetails.guests > 1 ? 's' : ''}</div>
              </div>
            </div>
          </div>
        </div>

        {/* Payment Info */}
        <div className="bg-white rounded-xl shadow-lg p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-6">Payment information</h2>
          
          <div className="flex items-center mb-4">
            <CreditCard className="h-6 w-6 text-gray-500 mr-3" />
            <div>
              <div className="text-sm font-medium text-gray-900">Credit card ending in 4242</div>
              <div className="text-sm text-gray-600">Visa</div>
            </div>
            <div className="ml-auto">
              <span className="text-sm text-green-600 font-medium">✓ Verified</span>
            </div>
          </div>
          
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex items-start">
              <Shield className="h-5 w-5 text-green-600 mt-0.5 mr-3 flex-shrink-0" />
              <div className="text-sm">
                <p className="font-medium text-green-900 mb-1">Payment protected</p>
                <p className="text-green-700">Your payment information is encrypted and secure. You can cancel for free within 48 hours.</p>
              </div>
            </div>
          </div>
        </div>

        {/* Cancellation Policy */}
        <div className="bg-white rounded-xl shadow-lg p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-6">Cancellation policy</h2>
          
          <div className="space-y-4">
            <div className="flex items-start">
              <Clock className="h-5 w-5 text-green-600 mt-0.5 mr-3 flex-shrink-0" />
              <div className="text-sm">
                <p className="font-medium text-gray-900 mb-1">Free cancellation for 48 hours</p>
                <p className="text-gray-600">Cancel before {new Date(checkInDate.getTime() - 48 * 60 * 60 * 1000).toLocaleDateString()} for a full refund.</p>
              </div>
            </div>
            
            <div className="flex items-start">
              <div className="w-5 h-5 rounded-full bg-yellow-100 flex items-center justify-center mt-0.5 mr-3 flex-shrink-0">
                <div className="w-2 h-2 rounded-full bg-yellow-500"></div>
              </div>
              <div className="text-sm">
                <p className="font-medium text-gray-900 mb-1">Partial refund until check-in</p>
                <p className="text-gray-600">Cancel between now and check-in for a 50% refund.</p>
              </div>
            </div>
          </div>
        </div>

        {/* Ground Rules */}
        <div className="bg-white rounded-xl shadow-lg p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-6">Ground rules</h2>
          
          <div className="space-y-3 text-sm text-gray-600">
            <p>• Check-in time: 3:00 PM - 11:00 PM</p>
            <p>• Check-out time: 11:00 AM</p>
            <p>• Maximum {room.capacity} guests</p>
            <p>• No smoking inside the property</p>
            <p>• No pets allowed</p>
            <p>• No parties or events</p>
            <p>• Respect prayer times and local customs</p>
          </div>
        </div>

        {/* Terms and Conditions */}
        <div className="bg-white rounded-xl shadow-lg p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-6">Terms and conditions</h2>
          
          <div className="bg-gray-50 rounded-lg p-4 mb-4 max-h-40 overflow-y-auto text-sm text-gray-600">
            <p className="mb-3">By clicking "Confirm and pay", you agree to the following terms:</p>
            <p className="mb-2">1. You are authorized to use the payment method provided</p>
            <p className="mb-2">2. You will respect the property and follow house rules</p>
            <p className="mb-2">3. You acknowledge the cancellation policy stated above</p>
            <p className="mb-2">4. You are responsible for all guests in your party</p>
            <p className="mb-2">5. You will provide accurate contact information</p>
            <p className="mb-2">6. You understand that this is a legally binding reservation</p>
            <p>7. You agree to World Hospitality&apos;s Terms of Service and Privacy Policy</p>
          </div>
          
          <label className="flex items-start">
            <input
              type="checkbox"
              checked={acceptedTerms}
              onChange={(e) => setAcceptedTerms(e.target.checked)}
              className="mt-1 h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
            />
            <span className="ml-3 text-sm text-gray-700">
              I agree to the terms and conditions, ground rules, and cancellation policy.
            </span>
          </label>
        </div>
      </div>

      {/* Booking Summary Sidebar */}
      <div className="lg:col-span-1">
        <div className="bg-white rounded-xl shadow-lg p-6 sticky top-8">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Booking summary</h3>
          
          {/* Price Breakdown */}
          <div className="space-y-3 mb-6">
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">${room.price} × {bookingDetails.nights} nights</span>
              <span className="text-gray-900">${bookingDetails.subtotal}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Service fee</span>
              <span className="text-gray-900">${bookingDetails.serviceFee}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Taxes</span>
              <span className="text-gray-900">${bookingDetails.taxes}</span>
            </div>
            <div className="border-t border-gray-200 pt-3">
              <div className="flex justify-between items-center">
                <span className="text-lg font-semibold text-gray-900">Total (USD)</span>
                <span className="text-2xl font-bold text-gray-900">${bookingDetails.total}</span>
              </div>
            </div>
          </div>

          {/* Confirm Button */}
          <button
            onClick={handleConfirmBooking}
            disabled={!acceptedTerms || isSubmitting}
            className="w-full bg-gradient-to-r from-blue-500 to-blue-600 text-white py-4 px-6 rounded-lg font-semibold hover:from-blue-600 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSubmitting ? (
              <div className="flex items-center justify-center">
                <div className="animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent mr-2"></div>
                Confirming booking...
              </div>
            ) : (
              'Confirm and pay'
            )}
          </button>

          <p className="text-xs text-gray-500 mt-3 text-center">
            You won&apos;t be charged until your booking is confirmed
          </p>
        </div>
      </div>
    </div>
  )
} 