'use client'

import { useState } from 'react'
import { <PERSON>lider } from '@/components/ui/slider'
import { Checkbox } from '@/components/ui/checkbox'
import { Button } from '@/components/ui/button'
import { X, Filter, Star } from 'lucide-react'

interface FilterState {
  priceRange: [number, number]
  roomTypes: string[]
  amenities: string[]
  rating: number
  distanceToHaram: number
}

interface AdvancedFiltersProps {
  isOpen: boolean
  onClose: () => void
  filters: FilterState
  onFiltersChange: (filters: FilterState) => void
  onApplyFilters: () => void
  onClearFilters: () => void
}

export const AdvancedFilters = ({
  isOpen,
  onClose,
  filters,
  onFiltersChange,
  onApplyFilters,
  onClearFilters
}: AdvancedFiltersProps) => {
  const roomTypes = [
    { id: 'standard', label: 'Standard Room', count: 3 },
    { id: 'deluxe', label: 'Deluxe Room', count: 1 },
    { id: 'suite', label: 'Suite', count: 3 },
    { id: 'family', label: 'Family Room', count: 1 }
  ]

  const amenitiesList = [
    { id: 'Free WiFi', label: 'Free WiFi', count: 8 },
    { id: 'Air Conditioning', label: 'Air Conditioning', count: 7 },
    { id: 'Prayer Mat', label: 'Prayer Mat', count: 8 },
    { id: 'Balcony', label: 'Balcony', count: 4 },
    { id: 'Room Service', label: 'Room Service', count: 3 },
    { id: 'Mini Fridge', label: 'Mini Fridge', count: 2 },
    { id: 'Kitchenette', label: 'Kitchenette', count: 1 },
    { id: 'City View', label: 'City View', count: 2 },
    { id: 'Haram View', label: 'Haram View', count: 1 },
    { id: 'Concierge', label: 'Concierge Service', count: 2 }
  ]

  const updateFilters = (updates: Partial<FilterState>) => {
    onFiltersChange({ ...filters, ...updates })
  }

  const toggleRoomType = (roomType: string) => {
    const newRoomTypes = filters.roomTypes.includes(roomType)
      ? filters.roomTypes.filter(type => type !== roomType)
      : [...filters.roomTypes, roomType]
    updateFilters({ roomTypes: newRoomTypes })
  }

  const toggleAmenity = (amenity: string) => {
    const newAmenities = filters.amenities.includes(amenity)
      ? filters.amenities.filter(am => am !== amenity)
      : [...filters.amenities, amenity]
    updateFilters({ amenities: newAmenities })
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 overflow-y-auto">
      <div className="min-h-screen px-4 text-center">
        <div className="inline-block w-full max-w-md p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-2xl">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900 flex items-center">
              <Filter className="w-5 h-5 mr-2" />
              Filters
            </h3>
            <button
              onClick={onClose}
              className="p-1 hover:bg-gray-100 rounded-full transition-colors"
            >
              <X className="w-5 h-5 text-gray-500" />
            </button>
          </div>

          <div className="space-y-6 max-h-96 overflow-y-auto">
            {/* Price Range */}
            <div>
              <h4 className="font-medium text-gray-900 mb-3">Price per night</h4>
              <div className="px-3">
                <Slider
                  value={filters.priceRange}
                  onValueChange={(value: number[]) => updateFilters({ priceRange: value as [number, number] })}
                  max={3000}
                  min={400}
                  step={50}
                  className="w-full"
                />
                <div className="flex justify-between text-sm text-gray-600 mt-2">
                  <span>${filters.priceRange[0]}</span>
                  <span>${filters.priceRange[1]}+</span>
                </div>
              </div>
            </div>

            {/* Room Type */}
            <div>
              <h4 className="font-medium text-gray-900 mb-3">Room type</h4>
              <div className="space-y-2">
                {roomTypes.map((type) => (
                  <label key={type.id} className="flex items-center space-x-3 cursor-pointer">
                    <Checkbox
                      checked={filters.roomTypes.includes(type.id)}
                      onCheckedChange={() => toggleRoomType(type.id)}
                    />
                    <span className="text-sm text-gray-700 flex-1">{type.label}</span>
                    <span className="text-xs text-gray-500">({type.count})</span>
                  </label>
                ))}
              </div>
            </div>

            {/* Rating */}
            <div>
              <h4 className="font-medium text-gray-900 mb-3">Rating</h4>
              <div className="space-y-2">
                {[4.5, 4.0, 3.5, 3.0].map((rating) => (
                  <label key={rating} className="flex items-center space-x-3 cursor-pointer">
                    <input
                      type="radio"
                      name="rating"
                      value={rating}
                      checked={filters.rating === rating}
                      onChange={(e) => updateFilters({ rating: parseFloat(e.target.value) })}
                      className="w-4 h-4 text-blue-600"
                    />
                    <div className="flex items-center">
                      <Star className="w-4 h-4 text-yellow-400 fill-current" />
                      <span className="text-sm text-gray-700 ml-1">{rating}+</span>
                    </div>
                  </label>
                ))}
              </div>
            </div>

            {/* Distance to Haram */}
            <div>
              <h4 className="font-medium text-gray-900 mb-3">Distance to Holy Mosque</h4>
              <div className="px-3">
                <Slider
                  value={[filters.distanceToHaram]}
                  onValueChange={(value: number[]) => updateFilters({ distanceToHaram: value[0] })}
                  max={1000}
                  min={50}
                  step={50}
                  className="w-full"
                />
                <div className="flex justify-between text-sm text-gray-600 mt-2">
                  <span>50m</span>
                  <span>{filters.distanceToHaram}m max</span>
                </div>
              </div>
            </div>

            {/* Amenities */}
            <div>
              <h4 className="font-medium text-gray-900 mb-3">Amenities</h4>
              <div className="space-y-2 max-h-40 overflow-y-auto">
                {amenitiesList.map((amenity) => (
                  <label key={amenity.id} className="flex items-center space-x-3 cursor-pointer">
                    <Checkbox
                      checked={filters.amenities.includes(amenity.id)}
                      onCheckedChange={() => toggleAmenity(amenity.id)}
                    />
                    <span className="text-sm text-gray-700 flex-1">{amenity.label}</span>
                    <span className="text-xs text-gray-500">({amenity.count})</span>
                  </label>
                ))}
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex space-x-3 mt-6 pt-6 border-t border-gray-200">
            <Button
              variant="outline"
              onClick={onClearFilters}
              className="flex-1"
            >
              Clear all
            </Button>
            <Button
              onClick={() => {
                onApplyFilters()
                onClose()
              }}
              className="flex-1 bg-blue-600 hover:bg-blue-700"
            >
              Show results
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
} 