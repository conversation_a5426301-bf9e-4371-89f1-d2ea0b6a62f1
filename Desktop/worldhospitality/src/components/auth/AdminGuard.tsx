'use client'

import { useAuth } from '@/hooks/use-auth'
import { useRouter } from 'next/navigation'
import { useEffect } from 'react'

export function AdminGuard({ children }: { children: React.ReactNode }) {
  const { isAdmin } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!isAdmin) {
      // You can redirect to a login page or a "not authorized" page
      // For now, we'll just push them to the home page.
      router.push('/') 
    }
  }, [isAdmin, router])

  if (!isAdmin) {
    // You can also return a loading spinner here
    return <p>Loading...</p>
  }

  return <>{children}</>
} 