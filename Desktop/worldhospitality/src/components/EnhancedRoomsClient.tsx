'use client'

import { useState, useMemo } from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { Room } from '@/types'
import { Users, Star, Filter } from 'lucide-react'
import { AdvancedFilters } from './search/AdvancedFilters'
import { SortDropdown } from './search/SortDropdown'

interface FilterState {
  priceRange: [number, number]
  roomTypes: string[]
  amenities: string[]
  rating: number
  distanceToHaram: number
}

interface EnhancedRoomsClientProps {
  rooms: Room[]
  lng: string
  searchParams: {
    location?: string
    checkin?: string
    checkout?: string
    guests?: number
  }
}

export const EnhancedRoomsClient = ({ rooms, lng, searchParams }: EnhancedRoomsClientProps) => {
  const [showFilters, setShowFilters] = useState(false)
  const [sortBy, setSortBy] = useState('recommended')
  const [filters, setFilters] = useState<FilterState>({
    priceRange: [400, 3000],
    roomTypes: [],
    amenities: [],
    rating: 0,
    distanceToHaram: 1000
  })

  // Apply filters to rooms
  const filteredRooms = useMemo(() => {
    return rooms.filter(room => {
      // Price filter
      if (room.price < filters.priceRange[0] || room.price > filters.priceRange[1]) {
        return false
      }

      // Room type filter
      if (filters.roomTypes.length > 0 && !filters.roomTypes.includes(room.roomType)) {
        return false
      }

      // Amenities filter
      if (filters.amenities.length > 0) {
        const hasAllAmenities = filters.amenities.every(amenity => 
          room.amenities.includes(amenity)
        )
        if (!hasAllAmenities) return false
      }

      // Rating filter
      if (filters.rating > 0 && room.rating < filters.rating) {
        return false
      }

      // Distance filter
      if (room.distanceToHaram > filters.distanceToHaram) {
        return false
      }

      return true
    })
  }, [rooms, filters])

  // Apply sorting
  const sortedRooms = useMemo(() => {
    const sorted = [...filteredRooms]
    
    switch (sortBy) {
      case 'price-low':
        return sorted.sort((a, b) => a.price - b.price)
      case 'price-high':
        return sorted.sort((a, b) => b.price - a.price)
      case 'rating':
        return sorted.sort((a, b) => b.rating - a.rating)
      case 'distance':
        return sorted.sort((a, b) => a.distanceToHaram - b.distanceToHaram)
      default: // recommended
        return sorted.sort((a, b) => b.rating * b.reviewCount - a.rating * a.reviewCount)
    }
  }, [filteredRooms, sortBy])

  const clearFilters = () => {
    setFilters({
      priceRange: [400, 3000],
      roomTypes: [],
      amenities: [],
      rating: 0,
      distanceToHaram: 1000
    })
  }

  const hasActiveFilters = () => {
    return (
      filters.priceRange[0] > 400 ||
      filters.priceRange[1] < 3000 ||
      filters.roomTypes.length > 0 ||
      filters.amenities.length > 0 ||
      filters.rating > 0 ||
      filters.distanceToHaram < 1000
    )
  }

  return (
    <>
      <div className="flex">
        {/* Properties List */}
        <div className="w-full overflow-y-auto">
          <div className="container mx-auto px-6 py-6">
            {/* Results Header with Filters */}
            <div className="flex items-center justify-between mb-6">
              <div>
                <h1 className="text-xl font-semibold text-gray-900">
                  {sortedRooms.length > 0 ? `${sortedRooms.length} stays` : 'No stays'} in Makkah
                </h1>
                {(searchParams.location || searchParams.checkin || searchParams.checkout || (searchParams.guests && searchParams.guests > 1)) && (
                  <div className="flex flex-wrap gap-2 mt-1 text-sm text-gray-600">
                    {searchParams.checkin && searchParams.checkout && (
                      <span>{searchParams.checkin} - {searchParams.checkout}</span>
                    )}
                    {searchParams.guests && searchParams.guests > 1 && <span>• {searchParams.guests} guests</span>}
                    {searchParams.location && <span>• {searchParams.location}</span>}
                  </div>
                )}
              </div>
              <div className="flex items-center gap-4">
                <button 
                  onClick={() => setShowFilters(true)}
                  className={`flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors ${
                    hasActiveFilters() ? 'bg-blue-50 border-blue-300 text-blue-700' : ''
                  }`}
                >
                  <Filter className="w-4 h-4" />
                  <span className="text-sm">Filters</span>
                  {hasActiveFilters() && (
                    <span className="bg-blue-600 text-white text-xs px-1.5 py-0.5 rounded-full">
                      {[
                        filters.roomTypes.length,
                        filters.amenities.length,
                        filters.rating > 0 ? 1 : 0,
                        filters.priceRange[0] > 400 || filters.priceRange[1] < 3000 ? 1 : 0,
                        filters.distanceToHaram < 1000 ? 1 : 0
                      ].reduce((sum, count) => sum + count, 0)}
                    </span>
                  )}
                </button>
                <SortDropdown sortBy={sortBy} onSortChange={setSortBy} />
              </div>
            </div>

            {sortedRooms.length === 0 ? (
              <div className="text-center py-12">
                <div className="max-w-md mx-auto">
                  <h2 className="text-xl font-semibold text-gray-900 mb-2">
                    No properties found
                  </h2>
                  <p className="text-gray-600 mb-4">
                    Try adjusting your search criteria or browse all available rooms.
                  </p>
                  {hasActiveFilters() && (
                    <button
                      onClick={clearFilters}
                      className="text-blue-600 hover:text-blue-700 font-medium"
                    >
                      Clear all filters
                    </button>
                  )}
                </div>
              </div>
            ) : (
              <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
                {sortedRooms.map((room) => (
                  <Link
                    key={room.id}
                    href={`/${lng}/rooms/${room.id}?${new URLSearchParams({
                      ...(searchParams.checkin && { checkin: searchParams.checkin }),
                      ...(searchParams.checkout && { checkout: searchParams.checkout }),
                      ...(searchParams.guests && { guests: searchParams.guests.toString() })
                    }).toString()}`}
                    className="group cursor-pointer"
                  >
                    <div className="bg-white rounded-xl overflow-hidden hover:shadow-lg transition-all duration-300">
                      {/* Image */}
                      <div className="relative h-64 overflow-hidden">
                        <Image
                          src={room.image}
                          alt={room.name}
                          fill
                          className="object-cover group-hover:scale-105 transition-transform duration-300"
                        />
                        <div className="absolute top-3 right-3 bg-white rounded-full p-2 shadow-sm opacity-0 group-hover:opacity-100 transition-opacity">
                          <svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                          </svg>
                        </div>
                      </div>

                      {/* Content */}
                      <div className="p-4">
                        {/* Location and Rating */}
                        <div className="flex items-center justify-between mb-1">
                          <span className="text-sm font-medium text-gray-900">
                            {Math.round(room.distanceToHaram)}m to Holy Mosque
                          </span>
                          <div className="flex items-center">
                            <Star className="w-3 h-3 text-yellow-400 fill-current mr-1" />
                            <span className="text-xs text-gray-600">{room.rating}</span>
                            <span className="text-xs text-gray-500 ml-1">({room.reviewCount})</span>
                          </div>
                        </div>

                        {/* Distance */}
                        <div className="text-xs text-gray-500 mb-1">
                          {room.distanceToHaram < 200 ? 'Very close' : room.distanceToHaram < 500 ? 'Walking distance' : 'Short ride'} to Holy Mosque
                        </div>

                        {/* Dates */}
                        {searchParams.checkin && searchParams.checkout && (
                          <div className="text-xs text-gray-500 mb-2">
                            {new Date(searchParams.checkin).toLocaleDateString()} - {new Date(searchParams.checkout).toLocaleDateString()}
                          </div>
                        )}

                        {/* Room Name */}
                        <h3 className="font-medium text-gray-900 mb-1 group-hover:text-blue-600 transition-colors">
                          {room.name}
                        </h3>

                        {/* Room Type & Capacity */}
                        <div className="flex items-center justify-between text-gray-600 mb-2">
                          <span className="text-sm capitalize">{room.roomType} • {room.bedType}</span>
                          <div className="flex items-center">
                            <Users className="w-4 h-4 mr-1" />
                            <span className="text-sm">Up to {room.capacity}</span>
                          </div>
                        </div>

                        {/* Price */}
                        <div className="flex items-baseline">
                          <span className="text-lg font-semibold text-gray-900">${room.price}</span>
                          <span className="text-sm text-gray-500 ml-1">night</span>
                        </div>
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Advanced Filters Modal */}
      <AdvancedFilters
        isOpen={showFilters}
        onClose={() => setShowFilters(false)}
        filters={filters}
        onFiltersChange={setFilters}
        onApplyFilters={() => {}}
        onClearFilters={clearFilters}
      />
    </>
  )
} 