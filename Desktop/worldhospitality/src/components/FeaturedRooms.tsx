import { supabase } from '@/lib/supabaseClient'
import { transformRoomData, formatPrice, getMainRoomImage } from '@/lib/room-utils'
import { Room } from '@/types'
import Link from 'next/link'
import Image from 'next/image'
import { Star } from 'lucide-react'

interface FeaturedRoomsProps {
  lng: string
  t: (key: string) => string
}

export async function FeaturedRooms({ lng, t }: FeaturedRoomsProps) {
  // Fetch top 3 rooms by rating for featured section
  const { data: roomsData, error } = await supabase
    .from('rooms')
    .select('*')
    .order('rating', { ascending: false })
    .limit(3)

  if (error || !roomsData || roomsData.length === 0) {
    // Fallback content if no real rooms available
    return (
      <section className="container mx-auto px-6 mb-16">
        <h2 className="text-2xl font-semibold text-gray-900 mb-8">
          {t('homepage.featured_section.title')}
        </h2>
        <div className="text-center text-gray-600">
          <p>No accommodations available at the moment.</p>
          <p className="mt-2">Please check back later or contact us directly.</p>
        </div>
      </section>
    )
  }

  const rooms: Room[] = roomsData.map(transformRoomData)

  return (
    <section className="container mx-auto px-6 mb-16">
      <h2 className="text-2xl font-semibold text-gray-900 mb-8">
        {t('homepage.featured_section.title')}
      </h2>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {rooms.map((room) => (
          <Link 
            key={room.id} 
            href={`/${lng}/rooms/${room.id}`}
            className="group block"
          >
            <div className="bg-white rounded-xl overflow-hidden hover:shadow-lg transition-shadow">
              {/* Room Image */}
              <div className="relative h-64 bg-gray-200">
                <Image
                  src={getMainRoomImage(room)}
                  alt={room.name}
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute top-3 right-3 bg-white rounded-full px-2 py-1 text-sm font-medium flex items-center gap-1">
                  <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                  {room.rating}
                </div>
              </div>

              {/* Room Details */}
              <div className="p-4">
                <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2">
                  {room.name}
                </h3>
                
                <div className="flex items-center gap-2 text-sm text-gray-600 mb-3">
                  <span>{room.capacity} guests</span>
                  <span>•</span>
                  <span>{room.distanceToHaram}m to Haram</span>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <span className="text-xl font-bold text-gray-900">
                      {formatPrice(room.price, room.priceCurrency)}
                    </span>
                    <span className="text-gray-600 text-sm ml-1">
                      {t('homepage.featured_section.per_night')}
                    </span>
                  </div>
                  
                  <div className="text-sm text-gray-600">
                    {room.reviewCount} {t('homepage.featured_section.reviews')}
                  </div>
                </div>

                {/* Amenities Preview */}
                <div className="mt-3 flex flex-wrap gap-1">
                  {room.amenities.slice(0, 3).map((amenity, index) => (
                    <span 
                      key={index}
                      className="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded"
                    >
                      {amenity}
                    </span>
                  ))}
                  {room.amenities.length > 3 && (
                    <span className="text-xs text-gray-500 px-2 py-1">
                      +{room.amenities.length - 3} more
                    </span>
                  )}
                </div>
              </div>
            </div>
          </Link>
        ))}
      </div>

      {/* View All Button */}
      <div className="text-center mt-8">
        <Link 
          href={`/${lng}/rooms`}
          className="inline-flex items-center px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors"
        >
          View All Accommodations
        </Link>
      </div>
    </section>
  )
} 