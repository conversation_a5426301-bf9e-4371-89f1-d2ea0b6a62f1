'use client'

import { useState } from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { Room } from '@/types'
import { Users } from 'lucide-react'

interface RoomsClientProps {
  rooms: Room[]
  lng: string
  searchParams: {
    location?: string
    checkin?: string
    checkout?: string
    guests?: number
  }
}

export const RoomsClient = ({ rooms, lng, searchParams }: RoomsClientProps) => {
  return (
    <div className="flex">
      {/* Properties List */}
      <div className="w-full overflow-y-auto">
        <div className="container mx-auto px-6 py-6">
          {rooms.length === 0 ? (
            <div className="text-center py-12">
              <h2 className="text-xl font-semibold text-gray-900 mb-2">
                No properties found
              </h2>
              <p className="text-gray-600">
                Try adjusting your search criteria or browse all available rooms.
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
              {rooms.map((room) => (
                <Link
                  key={room.id}
                  href={`/${lng}/rooms/${room.id}?${new URLSearchParams({
                    ...(searchParams.checkin && { checkin: searchParams.checkin }),
                    ...(searchParams.checkout && { checkout: searchParams.checkout }),
                    ...(searchParams.guests && { guests: searchParams.guests.toString() })
                  }).toString()}`}
                  id={`room-${room.id}`}
                  className="group cursor-pointer"
                >
                  <div className="bg-white rounded-xl overflow-hidden hover:shadow-lg transition-all duration-300">
                    {/* Image */}
                    <div className="relative h-64 overflow-hidden">
                      <Image
                        src={room.image}
                        alt={room.name}
                        fill
                        className="object-cover group-hover:scale-105 transition-transform duration-300"
                      />
                      <div className="absolute top-3 right-3 bg-white rounded-full p-2 shadow-sm opacity-0 group-hover:opacity-100 transition-opacity">
                        <svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                        </svg>
                      </div>
                    </div>

                    {/* Content */}
                    <div className="p-4">
                      {/* Location and Rating */}
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-sm font-medium text-gray-900">
                          Near Holy Mosque, Makkah
                        </span>
                        <div className="flex items-center">
                          <svg className="w-3 h-3 text-gray-900 fill-current mr-1" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                          </svg>
                          <span className="text-xs text-gray-600">4.9</span>
                        </div>
                      </div>

                      {/* Distance */}
                      <div className="text-xs text-gray-500 mb-1">
                        5 minutes walk to Holy Mosque
                      </div>

                      {/* Dates */}
                      {searchParams.checkin && searchParams.checkout && (
                        <div className="text-xs text-gray-500 mb-2">
                          {new Date(searchParams.checkin).toLocaleDateString()} - {new Date(searchParams.checkout).toLocaleDateString()}
                        </div>
                      )}

                      {/* Room Name */}
                      <h3 className="font-medium text-gray-900 mb-1 group-hover:text-blue-600 transition-colors">
                        {room.name}
                      </h3>

                      {/* Capacity */}
                      <div className="flex items-center text-gray-600 mb-2">
                        <Users className="w-4 h-4 mr-1" />
                        <span className="text-sm">Up to {room.capacity} guests</span>
                      </div>

                      {/* Price */}
                      <div className="flex items-baseline">
                        <span className="text-lg font-semibold text-gray-900">${room.price}</span>
                        <span className="text-sm text-gray-500 ml-1">night</span>
                      </div>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  )
} 