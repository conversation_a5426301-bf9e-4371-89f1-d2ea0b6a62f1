'use client'

import { useState } from 'react'
import { Room } from '@/types'
import { Calendar, Users, Star } from 'lucide-react'

interface BookingFormProps {
  room: Room
  lng: string
}

export const BookingForm = ({ room }: BookingFormProps) => {
  const [checkIn, setCheckIn] = useState('')
  const [checkOut, setCheckOut] = useState('')
  const [guests, setGuests] = useState(1)
  const [showGuestPicker, setShowGuestPicker] = useState(false)

  const calculateNights = () => {
    if (!checkIn || !checkOut) return 0
    const start = new Date(checkIn)
    const end = new Date(checkOut)
    const diffTime = Math.abs(end.getTime() - start.getTime())
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return diffDays
  }

  const nights = calculateNights()
  const subtotal = nights * room.price
  const serviceFee = Math.round(subtotal * 0.14) // 14% service fee
  const taxes = Math.round(subtotal * 0.15) // 15% taxes
  const total = subtotal + serviceFee + taxes

  const handleReserve = () => {
    if (!checkIn || !checkOut) {
      alert('Please select your check-in and check-out dates')
      return
    }
    
    // Build URL parameters for the booking flow
    const params = new URLSearchParams()
    params.set('roomId', room.id.toString())
    if (checkIn) params.set('checkin', checkIn)
    if (checkOut) params.set('checkout', checkOut)
    if (guests > 1) params.set('guests', guests.toString())

    // Navigate to login page with booking parameters
    window.location.href = `/en/book/login?${params.toString()}`
  }

  return (
    <div className="border border-gray-300 rounded-xl p-6 shadow-lg bg-white">
      {/* Price Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <span className="text-2xl font-semibold">${room.price}</span>
          <span className="text-gray-600"> night</span>
        </div>
        <div className="flex items-center gap-1 text-sm">
          <Star className="h-4 w-4 text-yellow-400 fill-current" />
          <span className="font-semibold">4.9</span>
          <span className="text-gray-600">(127 reviews)</span>
        </div>
      </div>

      {/* Date Selection */}
      <div className="border border-gray-300 rounded-lg mb-4">
        <div className="grid grid-cols-2">
          <div className="p-3 border-r border-gray-300">
            <label className="text-xs font-semibold text-gray-900 block mb-1">
              CHECK-IN
            </label>
            <input
              type="date"
              value={checkIn}
              onChange={(e) => setCheckIn(e.target.value)}
              min={new Date().toISOString().split('T')[0]}
              className="w-full text-sm bg-transparent border-none outline-none"
              placeholder="Add date"
            />
          </div>
          <div className="p-3">
            <label className="text-xs font-semibold text-gray-900 block mb-1">
              CHECKOUT
            </label>
            <input
              type="date"
              value={checkOut}
              onChange={(e) => setCheckOut(e.target.value)}
              min={checkIn || new Date().toISOString().split('T')[0]}
              className="w-full text-sm bg-transparent border-none outline-none"
              placeholder="Add date"
            />
          </div>
        </div>
      </div>

      {/* Guest Selection */}
      <div className="relative mb-6">
        <div 
          className="border border-gray-300 rounded-lg p-3 cursor-pointer"
          onClick={() => setShowGuestPicker(!showGuestPicker)}
        >
          <label className="text-xs font-semibold text-gray-900 block mb-1">
            GUESTS
          </label>
          <div className="flex items-center justify-between">
            <span className="text-sm">
              {guests} guest{guests !== 1 ? 's' : ''}
            </span>
            <Users className="h-4 w-4 text-gray-400" />
          </div>
        </div>

        {showGuestPicker && (
          <div className="absolute top-full left-0 right-0 mt-2 bg-white border border-gray-300 rounded-lg shadow-lg z-10 p-4">
            <div className="flex items-center justify-between">
              <div>
                <div className="font-semibold text-gray-900">Guests</div>
                <div className="text-sm text-gray-600">Ages 13 or above</div>
              </div>
              <div className="flex items-center gap-3">
                <button
                  onClick={() => setGuests(Math.max(1, guests - 1))}
                  disabled={guests <= 1}
                  className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed hover:border-gray-900"
                >
                  −
                </button>
                <span className="w-8 text-center">{guests}</span>
                <button
                  onClick={() => setGuests(Math.min(room.capacity, guests + 1))}
                  disabled={guests >= room.capacity}
                  className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed hover:border-gray-900"
                >
                  +
                </button>
              </div>
            </div>
            <div className="mt-4 pt-4 border-t border-gray-200">
              <button
                onClick={() => setShowGuestPicker(false)}
                className="text-sm font-semibold text-gray-900 hover:text-gray-700"
              >
                Close
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Reserve Button */}
      <button
        onClick={handleReserve}
        className="w-full bg-gradient-to-r from-blue-500 to-blue-600 text-white py-3 rounded-lg font-semibold hover:from-blue-600 hover:to-blue-700 transition-all mb-4"
      >
        Reserve
      </button>

      <div className="text-center text-sm text-gray-600 mb-4">
        You won&apos;t be charged yet
      </div>

      {/* Price Breakdown */}
      {nights > 0 && (
        <div className="space-y-3">
          <div className="flex justify-between text-sm">
            <span className="underline">${room.price} x {nights} nights</span>
            <span>${subtotal}</span>
          </div>
          <div className="flex justify-between text-sm">
            <span className="underline">Service fee</span>
            <span>${serviceFee}</span>
          </div>
          <div className="flex justify-between text-sm">
            <span className="underline">Taxes</span>
            <span>${taxes}</span>
          </div>
          <div className="border-t border-gray-200 pt-3">
            <div className="flex justify-between font-semibold">
              <span>Total before taxes</span>
              <span>${total}</span>
            </div>
          </div>
        </div>
      )}

      {/* Additional Info */}
      <div className="mt-6 pt-6 border-t border-gray-200">
        <div className="space-y-3 text-sm text-gray-600">
          <div className="flex items-start gap-3">
            <div className="w-6 h-6 flex items-center justify-center">
              <Calendar className="h-4 w-4" />
            </div>
            <div>
              <div className="font-semibold text-gray-900">Free cancellation for 48 hours</div>
              <div>Get a full refund if you change your mind.</div>
            </div>
          </div>
          <div className="flex items-start gap-3">
            <div className="w-6 h-6 flex items-center justify-center">
              <Star className="h-4 w-4" />
            </div>
            <div>
                             <div className="font-semibold text-gray-900">This is a rare find</div>
               <div>Ahmad&apos;s place on World Hospitality is usually fully booked.</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
} 