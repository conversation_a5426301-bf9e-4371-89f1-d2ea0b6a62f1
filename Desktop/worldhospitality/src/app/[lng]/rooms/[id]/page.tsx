import { supabase } from '@/lib/supabaseClient'
import { transformRoomData, getRoomImages } from '@/lib/room-utils'
import { RoomDetailClient } from '@/components/RoomDetailClient'
import Image from 'next/image'
import Link from 'next/link'
import { 
  Users, 
  Star, 
  MapPin, 
  Wifi, 
  Car, 
  Coffee, 
  Shield, 
  Heart,
  Share,
  ChevronLeft
} from 'lucide-react'
import { BookingForm } from '@/components/BookingForm'
import { useTranslation } from '@/lib/i18n'

export default async function RoomDetailPage({ 
  params 
}: { 
  params: Promise<{ lng: string; id: string }> 
}) {
  const { lng, id } = await params
  const { t } = await useTranslation(lng, 'common')
  
  // Fetch the room by ID from Supabase
  const { data: roomData, error } = await supabase
    .from('rooms')
    .select('*')
    .eq('id', parseInt(id))
    .single()
  
  if (error || !roomData) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">{t('room.not_found')}</h1>
          <Link href={`/${lng}/rooms`} className="text-blue-600 hover:text-blue-700">
            {t('room.back_to_results')}
          </Link>
        </div>
      </div>
    )
  }

  // Transform the room data
  const room = transformRoomData(roomData)

  // Get room images (uploaded or fallback)
  const additionalImages = getRoomImages(room)

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <div className="border-b border-gray-200 bg-white">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <Link 
              href={`/${lng}/rooms`}
              className="flex items-center gap-2 text-gray-600 hover:text-gray-900 transition-colors"
            >
              <ChevronLeft className="h-5 w-5" />
              <span>{t('room.back_to_results')}</span>
            </Link>
            <div className="flex items-center gap-4">
              <button className="flex items-center gap-2 px-4 py-2 text-gray-700 hover:bg-gray-50 rounded-lg transition-colors">
                <Share className="h-4 w-4" />
                <span className="hidden sm:inline">{t('room.share')}</span>
              </button>
              <button className="flex items-center gap-2 px-4 py-2 text-gray-700 hover:bg-gray-50 rounded-lg transition-colors">
                <Heart className="h-4 w-4" />
                <span className="hidden sm:inline">{t('room.save')}</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-6 py-8">
        {/* Room Title and Clickable Image Gallery */}
        <RoomDetailClient room={room} images={additionalImages} />

        <div className="grid lg:grid-cols-3 gap-12">
          {/* Main Content */}
          <div className="lg:col-span-2">
            {/* Host Info */}
            <div className="border-b border-gray-200 pb-8 mb-8">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-xl font-semibold text-gray-900 mb-2">
                    {room.name}
                  </h2>
                  <div className="flex items-center gap-4 text-sm text-gray-600">
                    <span>{t('room.capacity', { count: room.capacity })}</span>
                    <span>•</span>
                    <span>{t('room.beds', { count: room.bedCount || 1, type: room.bedType || t('room.bed') })}</span>
                    <span>•</span>
                    <span>{t('room.bathrooms', { count: room.bathroomCount || 1 })}</span>
                  </div>
                </div>
                <div className="w-12 h-12 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center text-white font-semibold">
                  {room.name.charAt(0).toUpperCase()}
                </div>
              </div>
            </div>

            {/* Property Features */}
            <div className="border-b border-gray-200 pb-8 mb-8">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                {t('room.what_this_place_offers')}
              </h3>
              <div className="grid grid-cols-2 gap-4">
                {room.amenities.map((amenity, index) => (
                  <div key={index} className="flex items-center gap-3">
                    <div className="w-6 h-6 flex items-center justify-center">
                      {amenity.includes('WiFi') && <Wifi className="h-5 w-5 text-gray-600" />}
                      {amenity.includes('Car') && <Car className="h-5 w-5 text-gray-600" />}
                      {amenity.includes('Coffee') && <Coffee className="h-5 w-5 text-gray-600" />}
                      {!amenity.includes('WiFi') && !amenity.includes('Car') && !amenity.includes('Coffee') && (
                        <Shield className="h-5 w-5 text-gray-600" />
                      )}
                    </div>
                    <span className="text-gray-700">{t(`room.amenities.${amenity}`)}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Description */}
            <div className="border-b border-gray-200 pb-8 mb-8">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                {t('room.about_this_space')}
              </h3>
              <p className="text-gray-700 leading-relaxed">
                {room.description}
              </p>
            </div>

            {/* Sleep Arrangements */}
            <div className="border-b border-gray-200 pb-8 mb-8">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                {t('room.where_you_sleep')}
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="border border-gray-200 rounded-lg p-4">
                  <h4 className="font-semibold text-gray-900 mb-2">{t('room.sleeping_area')}</h4>
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <Users className="h-4 w-4" />
                    <span>{t('room.beds', { count: room.bedCount || 1, type: room.bedType || t('room.bed') })}</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm text-gray-600 mt-1">
                    <span>{t('room.accommodates_up_to', { count: room.capacity })}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Reviews */}
            <div className="mb-8">
              <div className="flex items-center gap-2 mb-6">
                <Star className="h-5 w-5 text-yellow-400 fill-current" />
                <h3 className="text-lg font-semibold text-gray-900">
                  {room.rating} · {room.reviewCount} {t('room.review', { count: room.reviewCount })}
                </h3>
              </div>
              
              {room.reviewCount > 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <p>{t('room.reviews_placeholder')}</p>
                  <p className="text-sm mt-2">{t('room.review_system_coming_soon')}</p>
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <p>{t('room.no_reviews_yet')}</p>
                  <p className="text-sm mt-2">{t('room.be_first_to_review')}</p>
                </div>
              )}
            </div>
          </div>

          {/* Booking Sidebar */}
          <div className="lg:col-span-1">
            <div className="sticky top-24">
              <BookingForm room={room} lng={lng} />
            </div>
          </div>
        </div>

        {/* Location */}
        <div className="mt-12 border-t border-gray-200 pt-12">
          <h3 className="text-lg font-semibold text-gray-900 mb-6">
            {t('room.where_you_be')}
          </h3>
          
          {room.googleMapsLink ? (
            <div className="space-y-4">
              {/* Google Maps Link Card */}
              <div className="border border-gray-200 rounded-lg p-6 bg-gradient-to-r from-blue-50 to-indigo-50">
                <div className="flex items-center gap-4">
                  <div className="flex-shrink-0">
                    <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center">
                      <MapPin className="h-6 w-6 text-white" />
                    </div>
                  </div>
                  <div className="flex-1">
                    <h4 className="text-lg font-semibold text-gray-900 mb-1">
                      {t('room.hotel_location')}
                    </h4>
                    <p className="text-gray-600 text-sm mb-3">
                      {t('room.click_to_view_location')}
                    </p>
                    <a 
                      href={room.googleMapsLink}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium"
                    >
                      <MapPin className="h-4 w-4" />
                      {t('room.open_in_google_maps')}
                    </a>
                  </div>
                </div>
              </div>
              
              {/* Additional location info */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h5 className="font-medium text-gray-900 mb-2">{t('room.getting_there')}</h5>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• {t('room.step_by_step_directions')}</li>
                  <li>• {t('room.use_google_maps')}</li>
                  <li>• {t('room.save_location_offline')}</li>
                  <li>• {t('room.share_with_family')}</li>
                </ul>
              </div>
            </div>
          ) : (
            <div className="bg-gray-100 rounded-lg h-64 flex items-center justify-center mb-4">
              <div className="text-center text-gray-500">
                <MapPin className="w-12 h-12 mx-auto mb-2" />
                <p>{t('room.location_not_available')}</p>
              </div>
            </div>
          )}
          
          <div className="mt-6">
            <h4 className="font-semibold text-gray-900 mb-2">{t('room.city_country')}</h4>
            <p className="text-gray-700 text-sm">
              {t('room.city_description')}
            </p>
          </div>
        </div>
      </div>
    </div>
  )
} 