import { useTranslation } from '@/lib/i18n'
import { supabase } from '@/lib/supabaseClient'
import { transformRoomData } from '@/lib/room-utils'
import { Room } from '@/types';
import { SearchBarWrapper } from '@/components/SearchBarWrapper'
import { EnhancedRoomsClient } from '@/components/EnhancedRoomsClient'

export default async function RoomsPage({ 
  params, 
  searchParams 
}: { 
  params: Promise<{ lng: string }>
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>
}) {
  const { lng } = await params;
  const search = await searchParams;

  // Extract search parameters
  const location = search.location as string || ''
  const checkin = search.checkin as string || ''
  const checkout = search.checkout as string || ''
  const guests = parseInt(search.guests as string || '1')

  // Fetch real rooms from Supabase
  const { data: roomsData, error } = await supabase
    .from('rooms')
    .select('*')
    .order('rating', { ascending: false })

  if (error) {
    console.error('Error fetching rooms:', error)
    // Fallback to empty array if error
    const filteredRooms: Room[] = []
    return <div>Error loading rooms</div>
  }

  // Transform the database data
  const rooms: Room[] = (roomsData || []).map(transformRoomData)

  // Filter rooms based on search criteria
  const filteredRooms = rooms.filter((room: Room) => {
    if (location && !room.name.toLowerCase().includes(location.toLowerCase())) {
      return false
    }
    if (guests > room.capacity) {
      return false
    }
    return true
  })

  return (
    <div className="min-h-screen bg-white">
      {/* Search Bar */}
      <section className="pt-20 pb-4 bg-white border-b border-gray-200">
        <div className="container mx-auto px-6">
          <SearchBarWrapper lng={lng} />
        </div>
      </section>

      {/* Main Content */}
      <EnhancedRoomsClient 
        rooms={filteredRooms}
        lng={lng}
        searchParams={{ location, checkin, checkout, guests }}
      />
    </div>
  )
} 