import { useTranslation } from '@/lib/i18n'

export default async function AccountPage({ params }: { params: Promise<{ lng: string }> }) {
  const { lng } = await params;
  // eslint-disable-next-line react-hooks/rules-of-hooks
  const { t } = await useTranslation(lng)

  return (
    <div className="min-h-screen bg-gradient-to-b from-blue-50 to-white">
      <div className="container mx-auto px-4 py-12">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-blue-900 mb-4">
            {t('navigation.my_account')}
          </h1>
          <p className="text-lg text-blue-700">
            Manage your account and bookings
          </p>
        </div>
        
        <div className="max-w-2xl mx-auto bg-white rounded-lg shadow-lg p-8">
          <p className="text-center text-gray-600">
            Account management features coming soon...
          </p>
        </div>
      </div>
    </div>
  )
} 