import { useTranslation } from '@/lib/i18n'
import Link from 'next/link'
import { Star } from 'lucide-react'
import { SearchBarWrapper } from '@/components/SearchBarWrapper'
import { FeaturedRooms } from '@/components/FeaturedRooms'

export default async function Home({ params }: { params: Promise<{ lng: string }> }) {
  const { lng } = await params;
  // eslint-disable-next-line react-hooks/rules-of-hooks
  const { t } = await useTranslation(lng)
  const isRTL = lng === 'ar'
  
  return (
    <div className={`min-h-screen bg-white ${isRTL ? 'font-tajawal' : 'font-inter'}`}>
      {/* Search Section - True Airbnb Style */}
      <section className="pt-20 pb-8 bg-white">
        <div className="container mx-auto px-6">
          <SearchBarWrapper lng={lng} />
        </div>
      </section>

      {/* Content Section */}
      <div className="pb-16">
        {/* Quick Filters */}
        <section className="container mx-auto px-6 mb-16">
          <div className="flex flex-wrap gap-4 justify-center">
            {[
              { labelKey: 'homepage.quick_filters.near_mosque', icon: "🕌" },
              { labelKey: 'homepage.quick_filters.family_rooms', icon: "👨‍👩‍👧‍👦" },
              { labelKey: 'homepage.quick_filters.luxury_suites', icon: "✨" },
              { labelKey: 'homepage.quick_filters.budget_friendly', icon: "💰" }
            ].map((filter, index) => (
              <button
                key={index}
                className="flex items-center gap-2 px-6 py-3 border border-gray-300 rounded-full hover:border-gray-900 transition-colors text-sm font-medium"
              >
                <span>{filter.icon}</span>
                {t(filter.labelKey)}
              </button>
            ))}
          </div>
        </section>

        {/* Featured Accommodations */}
        <FeaturedRooms lng={lng} t={t} />

        {/* Explore Areas */}
        <section className="container mx-auto px-6 mb-16">
          <h2 className="text-2xl font-semibold text-gray-900 mb-8">
            {t('homepage.areas_section.title')}
          </h2>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {[
              { nameKey: "homepage.areas_section.areas.al_haram", distance: `2 ${t('homepage.areas_section.distances.min_walk')}`, image: "🕌" },
              { nameKey: "homepage.areas_section.areas.ajyad", distance: `5 ${t('homepage.areas_section.distances.min_walk')}`, image: "🏢" },
              { nameKey: "homepage.areas_section.areas.aziziyah", distance: `10 ${t('homepage.areas_section.distances.min_drive')}`, image: "🏘️" },
              { nameKey: "homepage.areas_section.areas.abdiyah", distance: `15 ${t('homepage.areas_section.distances.min_drive')}`, image: "🌆" }
            ].map((area, index) => (
              <div key={index} className="p-4 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors">
                <div className="text-3xl mb-2">{area.image}</div>
                <div className="font-medium text-gray-900">{t(area.nameKey)}</div>
                <div className="text-sm text-gray-500">{area.distance}</div>
              </div>
            ))}
          </div>
        </section>

        {/* Simple CTA */}
        <section className="container mx-auto px-6 text-center">
          <div className="max-w-2xl mx-auto">
            <h2 className="text-3xl font-semibold text-gray-900 mb-4">
              {t('homepage.cta.title')} {t('homepage.cta.title_highlight')}
            </h2>
            <p className="text-lg text-gray-600 mb-8">
              {t('homepage.cta.description')}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href={`/${lng}/rooms`}
                className="px-8 py-3 bg-white border border-gray-900 text-gray-900 rounded-lg hover:bg-gray-50 transition-colors font-medium"
              >
                {t('homepage.cta.explore_rooms')}
              </Link>
              <Link
                href={`/${lng}/rooms`}
                className="px-8 py-3 bg-gray-900 text-white rounded-lg hover:bg-gray-800 transition-colors font-medium"
              >
                {t('navigation.book_now')}
              </Link>
            </div>
          </div>
        </section>
      </div>
    </div>
  )
} 