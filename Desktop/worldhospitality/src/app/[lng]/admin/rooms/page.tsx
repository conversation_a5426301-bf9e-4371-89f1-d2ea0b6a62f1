'use client'

import { useState, useEffect } from 'react'
import { supabase } from '@/lib/supabaseClient'
import { Room } from '@/types'
import { EnhancedRoomForm } from '@/components/admin/EnhancedRoomForm'
import { Button } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { TableSkeleton } from '@/components/admin/TableSkeleton'
import { Pencil, Trash2, Plus, Home, BedDouble, CalendarCheck } from 'lucide-react'
import { toast } from "sonner"
import { Toaster } from "@/components/ui/sonner"
import * as z from 'zod'
import Link from 'next/link'
import { useParams, usePathname } from 'next/navigation'

const formSchema = z.object({
  name: z.string().min(1, "Name is required"),
  description: z.string().min(1, "Description is required"),
  price: z.coerce.number().min(1, "Price must be greater than 0"),
  amenities: z.string().min(1, "Amenities are required"),
  capacity: z.coerce.number().min(1, "Capacity must be at least 1"),
  rating: z.coerce.number().min(1).max(5, "Rating must be between 1 and 5"),
  reviewCount: z.coerce.number().min(0, "Review count must be 0 or more"),
  roomType: z.enum(['standard', 'deluxe', 'suite', 'family']),
  distanceToHaram: z.coerce.number().min(1, "Distance must be positive"),
  size: z.coerce.number().min(1, "Size must be positive"),
  bedType: z.string().min(1, "Bed type is required"),
  hasBalcony: z.boolean().optional(),
  hasKitchen: z.boolean().optional(),
  hasBathroom: z.boolean().optional(),
})

export default function AdminRoomsPage() {
  const [rooms, setRooms] = useState<Room[]>([])
  const [loading, setLoading] = useState(true)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [selectedRoom, setSelectedRoom] = useState<Room | null>(null)
  
  const params = useParams()
  const pathname = usePathname()
  const lng = params.lng as string

  // Hide header and footer for admin pages
  useEffect(() => {
    const header = document.querySelector('header')
    const footer = document.querySelector('footer')
    if (header) header.style.display = 'none'
    if (footer) footer.style.display = 'none'

    return () => {
      if (header) header.style.display = ''
      if (footer) footer.style.display = ''
    }
  }, [])

  // Transform database fields to match TypeScript interface
  const transformRoomData = (dbRoom: any): Room => {
    return {
      ...dbRoom,
      internalName: dbRoom.internal_name,
      priceCurrency: dbRoom.price_currency,
      priceUsd: dbRoom.price_usd,
      reviewCount: dbRoom.review_count,
      roomType: dbRoom.room_type,
      distanceToHaram: dbRoom.distance_to_haram,
      bedType: dbRoom.bed_type,
      bedCount: dbRoom.bed_count,
      maxGuests: dbRoom.max_guests,
      bathroomCount: dbRoom.bathroom_count,
      hasBalcony: dbRoom.has_balcony,
      hasKitchen: dbRoom.has_kitchen,
      hasBathroom: dbRoom.has_bathroom,
      mainImageIndex: dbRoom.main_image_index || 0,
      googleMapsLink: dbRoom.google_maps_link,
    };
  };

  useEffect(() => {
    fetchRooms()
  }, [])

  async function fetchRooms() {
    try {
      setLoading(true)
      const { data, error } = await supabase
        .from('rooms')
        .select('*')
        .order('id', { ascending: true })
      
      if (error) throw error
      setRooms((data || []).map(transformRoomData))
    } catch (error: any) {
      console.error('Error fetching rooms:', error)
      toast.error('Failed to fetch rooms')
    } finally {
      setLoading(false)
    }
  }

  const handleEdit = (room: Room) => {
    setSelectedRoom(room);
    setIsModalOpen(true);
  };

  const handleSave = async (formData: any, imageFiles?: FileList, mainImageIndex?: number) => {
    try {
      console.log('Starting room save process with data:', formData);
      console.log('Main image index:', mainImageIndex);
      console.log('Internal name from form:', formData.internalName);
      console.log('Selected room internal name:', selectedRoom?.internalName);
      
      let imageUrl = selectedRoom?.image || '';
      let imageUrls: string[] = [];
      
      // Handle multiple image uploads
      if (imageFiles && imageFiles.length > 0) {
        console.log('Uploading', imageFiles.length, 'images...');
        const uploadPromises = Array.from(imageFiles).map(async (file, index) => {
          const fileName = `${Date.now()}_${index}_${file.name}`;
          const { data: uploadData, error: uploadError } = await supabase.storage
            .from('rooms')
            .upload(fileName, file);
          
          if (uploadError) {
            console.error('Image upload error:', uploadError);
            throw uploadError;
          }
          
          const { data: { publicUrl } } = supabase.storage
            .from('rooms')
            .getPublicUrl(uploadData.path);
          
          return publicUrl;
        });
        
        imageUrls = await Promise.all(uploadPromises);
        imageUrl = imageUrls[0] || ''; // Set first image as main image
        console.log('Images uploaded successfully:', imageUrls);
      }

      // Combine existing images with new images if editing
      let finalImageUrls = imageUrls;
      if (selectedRoom && selectedRoom.images) {
        // If editing, combine existing images with new ones
        finalImageUrls = [...selectedRoom.images, ...imageUrls];
      }

      const roomData = {
        name: formData.name,
        internal_name: formData.internalName,
        description: formData.description,
        price: formData.price,
        price_currency: formData.priceCurrency,
        amenities: formData.amenities, // Already an array from enhanced form
        capacity: formData.capacity,
        rating: formData.rating,
        review_count: formData.reviewCount,
        room_type: formData.roomType,
        distance_to_haram: formData.distanceToHaram,
        size: formData.size,
        bed_type: formData.bedType,
        bed_count: formData.bedCount,
        max_guests: formData.maxGuests,
        bathroom_count: formData.bathroomCount,
        has_balcony: formData.hasBalcony || false,
        has_kitchen: formData.hasKitchen || false,
        has_bathroom: formData.hasBathroom !== false,
        image: imageUrl,
        images: finalImageUrls.length > 0 ? finalImageUrls : undefined,
        main_image_index: mainImageIndex || 0, // Save the main image index
        google_maps_link: formData.googleMapsLink || null, // Save the Google Maps link
      };

      console.log('Room data to save:', roomData);

      if (selectedRoom) {
        console.log('Updating existing room with ID:', selectedRoom.id);
        const { data: updatedRoom, error } = await supabase
          .from('rooms')
          .update(roomData)
          .eq('id', selectedRoom.id)
          .select()
          .single();
        
        if (error) {
          console.error('Database update error:', error);
          throw error;
        }
        
        console.log('Room updated successfully:', updatedRoom);
        setRooms(rooms.map(r => r.id === selectedRoom.id ? updatedRoom : r));
        toast.success("Room updated successfully!");
      } else {
        console.log('Creating new room...');
        const { data: newRoom, error } = await supabase
          .from('rooms')
          .insert(roomData)
          .select()
          .single();
        
        if (error) {
          console.error('Database insert error:', error);
          throw error;
        }
        
        console.log('Room created successfully:', newRoom);
        setRooms([newRoom, ...rooms]);
        toast.success("Room created successfully!");
      }
      
      setIsModalOpen(false);
      setSelectedRoom(null);
    } catch (error: any) {
      console.error('Full error object:', error);
      console.error('Error message:', error?.message);
      console.error('Error details:', error?.details);
      console.error('Error hint:', error?.hint);
      console.error('Error code:', error?.code);
      
      // Better error message for user
      let errorMessage = "Failed to save room.";
      if (error?.message) {
        errorMessage = error.message;
      } else if (error?.details) {
        errorMessage = error.details;
      } else if (typeof error === 'string') {
        errorMessage = error;
      }
      
      toast.error(errorMessage);
    }
  };

  const handleDelete = async (roomId: number) => {
    if (!confirm('Are you sure you want to delete this room?')) return;
    
    try {
      const { error } = await supabase
        .from('rooms')
        .delete()
        .eq('id', roomId);
      
      if (error) throw error;
      
      setRooms(rooms.filter(r => r.id !== roomId));
      toast.success("Room deleted successfully!");
    } catch (error: any) {
      console.error('Error deleting room:', error);
      toast.error("Failed to delete room.");
    }
  };

  const navLinks = [
    { href: `/${lng}/admin/dashboard`, label: 'Dashboard', icon: Home },
    { href: `/${lng}/admin/rooms`, label: 'Rooms', icon: BedDouble },
    { href: `/${lng}/admin/bookings`, label: 'Bookings', icon: CalendarCheck },
  ]

  return (
    <div className="flex h-screen bg-gray-100">
      {/* Sidebar */}
      <div className="w-64 bg-white shadow-sm">
        <div className="p-6">
          <h2 className="text-xl font-bold text-gray-900">Admin Panel</h2>
        </div>
        <nav className="mt-6">
          {navLinks.map((link) => {
            const isActive = pathname === link.href
            const Icon = link.icon
            return (
              <Link
                key={link.href}
                href={link.href}
                className={`flex items-center px-6 py-3 text-sm font-medium transition-colors ${
                  isActive
                    ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                }`}
              >
                <Icon className="mr-3 h-5 w-5" />
                {link.label}
              </Link>
            )
          })}
        </nav>
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-y-auto">
        {/* Header */}
        <div className="bg-white shadow-sm border-b">
          <div className="px-6 py-4">
            <h1 className="text-2xl font-bold text-gray-900">Rooms Management</h1>
            <p className="text-gray-600">Manage your hotel rooms and their details</p>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          <div className="bg-white rounded-lg shadow">
            <div className="p-6 border-b border-gray-200">
              <div className="flex justify-between items-center">
                <h2 className="text-xl font-semibold">All Rooms</h2>
                <Button 
                  onClick={() => {
                    setSelectedRoom(null);
                    setIsModalOpen(true);
                  }}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Room
                </Button>
              </div>
            </div>
            
            <div className="p-6">
              {loading ? (
                <TableSkeleton columns={6} rows={5} />
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Image</TableHead>
                      <TableHead>Name</TableHead>
                      <TableHead>Internal Name</TableHead>
                      <TableHead>Price</TableHead>
                      <TableHead>Description</TableHead>
                      <TableHead>Amenities</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {rooms.map((room) => (
                      <TableRow key={room.id}>
                        <TableCell>
                          {(() => {
                            // Use main image index to get the correct image
                            const images = room.images || [];
                            const mainIndex = room.mainImageIndex || 0;
                            const mainImage = images[mainIndex] || room.image;
                            
                            return mainImage ? (
                              <img 
                                src={mainImage} 
                                alt={room.name}
                                className="w-16 h-16 object-cover rounded"
                              />
                            ) : (
                              <div className="w-16 h-16 bg-gray-200 rounded flex items-center justify-center">
                                <span className="text-gray-400 text-xs">No image</span>
                              </div>
                            )
                          })()}
                        </TableCell>
                        <TableCell className="font-medium">{room.name}</TableCell>
                        <TableCell>
                          <code className="bg-gray-100 px-2 py-1 rounded text-sm font-mono">
                            {(room as any).internal_name || `ROOM-${room.id}`}
                          </code>
                        </TableCell>
                        <TableCell className="font-semibold">SAR {room.price}</TableCell>
                        <TableCell className="max-w-xs truncate">{room.description}</TableCell>
                        <TableCell>
                          <div className="flex flex-wrap gap-1">
                            {room.amenities?.slice(0, 3).map((amenity, index) => (
                              <span 
                                key={index} 
                                className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded"
                              >
                                {amenity}
                              </span>
                            ))}
                            {room.amenities?.length > 3 && (
                              <span className="text-gray-500 text-xs">
                                +{room.amenities.length - 3} more
                              </span>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex space-x-2">
                            <Button
                              variant="outline"
                              onClick={() => handleEdit(room)}
                              className="p-2"
                            >
                              <Pencil className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="outline"
                              onClick={() => handleDelete(room.id)}
                              className="p-2 text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </div>
          </div>
        </div>
      </div>

              <EnhancedRoomForm
          isOpen={isModalOpen}
          setIsOpen={setIsModalOpen}
          room={selectedRoom}
          onSave={handleSave}
        />
      
      <Toaster richColors />
    </div>
  )
} 