'use client'

import { useEffect } from 'react'
import { Home, BedDouble, CalendarCheck } from 'lucide-react'
import Link from 'next/link'
import { useParams, usePathname } from 'next/navigation'

export default function AdminBookingsPage() {
  const params = useParams()
  const pathname = usePathname()
  const lng = params.lng as string

  // Hide header and footer for admin pages
  useEffect(() => {
    const header = document.querySelector('header')
    const footer = document.querySelector('footer')
    if (header) header.style.display = 'none'
    if (footer) footer.style.display = 'none'

    return () => {
      if (header) header.style.display = ''
      if (footer) footer.style.display = ''
    }
  }, [])

  const navLinks = [
    { href: `/${lng}/admin/dashboard`, label: 'Dashboard', icon: Home },
    { href: `/${lng}/admin/rooms`, label: 'Rooms', icon: BedDouble },
    { href: `/${lng}/admin/bookings`, label: 'Bookings', icon: CalendarCheck },
  ]

  return (
    <div className="flex h-screen bg-gray-100">
      {/* Sidebar */}
      <div className="w-64 bg-white shadow-sm">
        <div className="p-6">
          <h2 className="text-xl font-bold text-gray-900">Admin Panel</h2>
        </div>
        <nav className="mt-6">
          {navLinks.map((link) => {
            const isActive = pathname === link.href
            const Icon = link.icon
            return (
              <Link
                key={link.href}
                href={link.href}
                className={`flex items-center px-6 py-3 text-sm font-medium transition-colors ${
                  isActive
                    ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                }`}
              >
                <Icon className="mr-3 h-5 w-5" />
                {link.label}
              </Link>
            )
          })}
        </nav>
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-y-auto">
        {/* Header */}
        <div className="bg-white shadow-sm border-b">
          <div className="px-6 py-4">
            <h1 className="text-2xl font-bold text-gray-900">Bookings Management</h1>
            <p className="text-gray-600">View and manage customer reservations</p>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">Recent Bookings</h2>
            <p className="text-gray-600">Bookings management functionality coming soon...</p>
          </div>
        </div>
      </div>
    </div>
  )
} 