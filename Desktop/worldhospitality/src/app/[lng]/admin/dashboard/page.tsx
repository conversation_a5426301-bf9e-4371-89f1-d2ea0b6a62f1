'use client'

import { useEffect, useState } from 'react'
import { DashboardStats } from '@/components/admin/DashboardStats'
import { RevenueChart } from '@/components/admin/RevenueChart'
import { RecentActivity } from '@/components/admin/RecentActivity'
import { QuickActions } from '@/components/admin/QuickActions'
import { TopRooms } from '@/components/admin/TopRooms'
import { Calendar, TrendingUp, Users, DollarSign, Home, Star, Plus, Settings, Download } from 'lucide-react'

export default function AdminDashboard() {
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Hide main site header/footer for admin pages
    const header = document.querySelector('header')
    const footer = document.querySelector('footer')
    if (header) header.style.display = 'none'
    if (footer) footer.style.display = 'none'

    // Simulate loading
    setTimeout(() => setIsLoading(false), 1000)

    return () => {
      if (header) header.style.display = 'block'
      if (footer) footer.style.display = 'block'
    }
  }, [])

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-4 border-blue-600 border-t-transparent mx-auto mb-4"></div>
          <p className="text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Admin Sidebar */}
      <div className="fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg">
        <div className="flex flex-col h-full">
          {/* Logo */}
          <div className="flex items-center justify-center h-16 px-4 bg-blue-600">
            <h1 className="text-white text-xl font-bold">WH Admin</h1>
          </div>
          
          {/* Navigation */}
          <nav className="flex-1 px-4 py-6 space-y-2">
            <a href="/en/admin/dashboard" className="flex items-center px-4 py-3 text-blue-600 bg-blue-50 rounded-lg font-medium">
              <TrendingUp className="w-5 h-5 mr-3" />
              Dashboard
            </a>
            <a href="/en/admin/rooms" className="flex items-center px-4 py-3 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
              <Home className="w-5 h-5 mr-3" />
              Rooms
            </a>
            <a href="/en/admin/bookings" className="flex items-center px-4 py-3 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
              <Calendar className="w-5 h-5 mr-3" />
              Bookings
            </a>
            <a href="/en/admin/users" className="flex items-center px-4 py-3 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
              <Users className="w-5 h-5 mr-3" />
              Users
            </a>
            <div className="border-t border-gray-200 my-4"></div>
            <a href="/en" className="flex items-center px-4 py-3 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
              <Star className="w-5 h-5 mr-3" />
              View Site
            </a>
            <a href="#" className="flex items-center px-4 py-3 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
              <Settings className="w-5 h-5 mr-3" />
              Settings
            </a>
          </nav>
        </div>
      </div>

      {/* Main Content */}
      <div className="ml-64">
        <div className="p-8">
          {/* Header */}
          <div className="flex items-center justify-between mb-8">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
              <p className="text-gray-600 mt-1">Welcome back! Here's what's happening at World Hospitality.</p>
            </div>
            <div className="flex items-center space-x-4">
              <button className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                <Download className="w-4 h-4 mr-2" />
                Export Data
              </button>
              <button className="flex items-center px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                <Plus className="w-4 h-4 mr-2" />
                Quick Add
              </button>
            </div>
          </div>

          {/* Stats Cards */}
          <DashboardStats />

          {/* Charts and Data */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <RevenueChart />
            <TopRooms />
          </div>

          {/* Bottom Section */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <RecentActivity />
            <QuickActions />
          </div>
        </div>
      </div>
    </div>
  )
} 