import { useTranslation } from '@/lib/i18n'
import { LoginForm } from '@/components/booking/LoginForm'

export default async function LoginPage({ 
  params,
  searchParams 
}: { 
  params: Promise<{ lng: string }>
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>
}) {
  const { lng } = await params;
  const search = await searchParams;
  const { t } = await useTranslation(lng, 'common')
  const isRTL = lng === 'ar'

  // Extract booking parameters
  const roomId = search.roomId as string
  const checkin = search.checkin as string
  const checkout = search.checkout as string
  const guests = search.guests as string

  return (
    <div className={`min-h-screen bg-gray-50 ${isRTL ? 'font-tajawal' : 'font-inter'}`}>
      <div className="flex flex-col justify-center py-12 sm:px-6 lg:px-8">
        <div className="sm:mx-auto sm:w-full sm:max-w-md">
          <div className="text-center">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              World Hospitality
            </h1>
            <h2 className="text-xl font-semibold text-gray-700 mb-8">
              {t('booking.login.title') || 'Log in or sign up'}
            </h2>
          </div>
        </div>

        <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
          <div className="bg-white py-8 px-4 shadow-lg sm:rounded-lg sm:px-10">
            <LoginForm 
              lng={lng}
              isRTL={isRTL}
              bookingParams={{
                roomId,
                checkin,
                checkout,
                guests
              }}
            />
          </div>
        </div>

        {/* Trust indicators */}
        <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
          <div className="text-center text-sm text-gray-600 space-y-2">
            <p>🔒 {t('booking.login.secure') || 'Your information is secure and encrypted'}</p>
            <p>✓ {t('booking.login.no_fees') || 'No hidden fees or charges'}</p>
            <p>📞 {t('booking.login.support') || '24/7 customer support'}</p>
          </div>
        </div>
      </div>
    </div>
  )
} 