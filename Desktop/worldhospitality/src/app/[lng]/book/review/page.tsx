import { useTranslation } from '@/lib/i18n'
import { ReviewBooking } from '@/components/booking/ReviewBooking'
import { supabase } from '@/lib/supabaseClient'
import { transformRoomData, calculateBookingTotals } from '@/lib/room-utils'
import { BackButton } from '@/components/BackButton'

export default async function ReviewPage({ 
  params,
  searchParams 
}: { 
  params: Promise<{ lng: string }>
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>
}) {
  const { lng } = await params;
  const search = await searchParams;
  const { t } = await useTranslation(lng, 'common')
  const isRTL = lng === 'ar'

  // Extract booking parameters
  const roomId = search.roomId as string
  const checkin = search.checkin as string
  const checkout = search.checkout as string
  const guests = search.guests as string

  // Fetch the room from Supabase
  const { data: roomData, error } = await supabase
    .from('rooms')
    .select('*')
    .eq('id', parseInt(roomId))
    .single()

  if (error || !roomData || !checkin || !checkout) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Booking Not Found</h1>
          <p className="text-gray-600">Please start your booking process again.</p>
        </div>
      </div>
    )
  }

  // Transform the room data
  const room = transformRoomData(roomData)

  // Calculate booking details
  const checkInDate = new Date(checkin)
  const checkOutDate = new Date(checkout)
  const nights = Math.ceil((checkOutDate.getTime() - checkInDate.getTime()) / (1000 * 60 * 60 * 24))
  const bookingTotals = calculateBookingTotals(room.price, nights)

  return (
    <div className={`min-h-screen bg-gray-50 ${isRTL ? 'font-tajawal' : 'font-inter'}`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center mb-4">
            <BackButton />
            <h1 className="text-2xl font-bold text-gray-900">
              Review your booking
            </h1>
          </div>
          
          {/* Progress indicators */}
          <div className="flex items-center space-x-4 text-sm">
            <div className="flex items-center text-green-600">
              <div className="w-6 h-6 bg-green-600 text-white rounded-full flex items-center justify-center text-xs mr-2">✓</div>
              Account
            </div>
            <div className="w-8 h-px bg-gray-300"></div>
            <div className="flex items-center text-green-600">
              <div className="w-6 h-6 bg-green-600 text-white rounded-full flex items-center justify-center text-xs mr-2">✓</div>
              Payment
            </div>
            <div className="w-8 h-px bg-gray-300"></div>
            <div className="flex items-center text-blue-600 font-semibold">
              <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs mr-2">3</div>
              Review
            </div>
          </div>
        </div>

        <ReviewBooking 
          lng={lng}
          isRTL={isRTL}
          room={room}
          bookingDetails={{
            checkin,
            checkout,
            guests: parseInt(guests || '1'),
            nights,
            subtotal: bookingTotals.subtotal,
            serviceFee: bookingTotals.serviceFee,
            taxes: bookingTotals.taxes,
            total: bookingTotals.total
          }}
        />
      </div>
    </div>
  )
} 