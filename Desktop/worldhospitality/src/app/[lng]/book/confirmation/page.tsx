import { useTranslation } from '@/lib/i18n'
import { supabase } from '@/lib/supabaseClient'
import { transformRoomData } from '@/lib/room-utils'
import { CheckCircle, Calendar, Users, MapPin, Phone, Mail, Download, Share } from 'lucide-react'
import Link from 'next/link'

export default async function ConfirmationPage({ 
  params,
  searchParams 
}: { 
  params: Promise<{ lng: string }>
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>
}) {
  const { lng } = await params;
  const search = await searchParams;
  const { t } = await useTranslation(lng, 'common')
  const isRTL = lng === 'ar'

  // Extract booking parameters
  const bookingId = search.bookingId as string
  const roomId = search.roomId as string
  const checkin = search.checkin as string
  const checkout = search.checkout as string
  const guests = search.guests as string

  // Fetch the room from Supabase
  const { data: roomData, error } = await supabase
    .from('rooms')
    .select('*')
    .eq('id', parseInt(roomId))
    .single()

  if (error || !roomData || !checkin || !checkout || !bookingId) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Booking Not Found</h1>
          <p className="text-gray-600">Please check your booking confirmation email.</p>
        </div>
      </div>
    )
  }

  // Transform the room data
  const room = transformRoomData(roomData)

  const checkInDate = new Date(checkin)
  const checkOutDate = new Date(checkout)
  const nights = Math.ceil((checkOutDate.getTime() - checkInDate.getTime()) / (1000 * 60 * 60 * 24))

  return (
    <div className={`min-h-screen bg-gray-50 ${isRTL ? 'font-tajawal' : 'font-inter'}`}>
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Success Header */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mb-4">
            <CheckCircle className="w-8 h-8 text-green-600" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Booking confirmed!
          </h1>
          <p className="text-lg text-gray-600">
            Your reservation has been successfully booked
          </p>
          <div className="mt-4 inline-flex items-center px-4 py-2 bg-blue-100 rounded-full">
            <span className="text-sm font-medium text-blue-800">
              Booking ID: <span className="font-mono">{bookingId}</span>
            </span>
          </div>
        </div>

        {/* Booking Details Card */}
        <div className="bg-white rounded-xl shadow-lg overflow-hidden mb-8">
          <div className="p-6">
            <div className="flex flex-col lg:flex-row gap-6">
              {/* Room Image */}
              <div className="w-full lg:w-64 h-48 lg:h-auto bg-gray-200 rounded-lg overflow-hidden flex-shrink-0">
                <img 
                  src={room.image} 
                  alt={room.name}
                  className="w-full h-full object-cover"
                />
              </div>

              {/* Booking Info */}
              <div className="flex-1">
                <h2 className="text-2xl font-semibold text-gray-900 mb-2">{room.name}</h2>
                <div className="flex items-center text-gray-600 mb-4">
                  <MapPin className="w-4 h-4 mr-1" />
                  <span>Near Holy Mosque, Makkah, Saudi Arabia</span>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-6">
                  <div className="flex items-center">
                    <Calendar className="h-5 w-5 text-blue-600 mr-3" />
                    <div>
                      <div className="text-sm font-medium text-gray-900">Check-in</div>
                      <div className="text-sm text-gray-600">{checkInDate.toLocaleDateString()}</div>
                      <div className="text-xs text-gray-500">After 3:00 PM</div>
                    </div>
                  </div>
                  
                  <div className="flex items-center">
                    <Calendar className="h-5 w-5 text-blue-600 mr-3" />
                    <div>
                      <div className="text-sm font-medium text-gray-900">Check-out</div>
                      <div className="text-sm text-gray-600">{checkOutDate.toLocaleDateString()}</div>
                      <div className="text-xs text-gray-500">Before 11:00 AM</div>
                    </div>
                  </div>
                  
                  <div className="flex items-center">
                    <Users className="h-5 w-5 text-blue-600 mr-3" />
                    <div>
                      <div className="text-sm font-medium text-gray-900">Guests</div>
                      <div className="text-sm text-gray-600">{guests || 1} guest{parseInt(guests || '1') > 1 ? 's' : ''}</div>
                      <div className="text-xs text-gray-500">{nights} night{nights > 1 ? 's' : ''}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="bg-gray-50 px-6 py-4">
            <div className="flex flex-wrap gap-3">
              <button className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                <Download className="w-4 h-4 mr-2" />
                Download confirmation
              </button>
              <button className="flex items-center px-4 py-2 border border-gray-300 bg-white text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                <Share className="w-4 h-4 mr-2" />
                Share booking
              </button>
              <button className="flex items-center px-4 py-2 border border-gray-300 bg-white text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                <Calendar className="w-4 h-4 mr-2" />
                Add to calendar
              </button>
            </div>
          </div>
        </div>

        {/* What's Next */}
        <div className="bg-white rounded-xl shadow-lg p-6 mb-8">
          <h3 className="text-xl font-semibold text-gray-900 mb-6">What&apos;s next?</h3>
          
          <div className="space-y-4">
            <div className="flex items-start">
              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mr-4">
                <span className="text-blue-600 font-semibold text-sm">1</span>
              </div>
              <div>
                <h4 className="font-medium text-gray-900 mb-1">Check your email</h4>
                <p className="text-gray-600 text-sm">We&apos;ve sent your booking confirmation and check-in details to your email address.</p>
              </div>
            </div>
            
            <div className="flex items-start">
              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mr-4">
                <span className="text-blue-600 font-semibold text-sm">2</span>
              </div>
              <div>
                <h4 className="font-medium text-gray-900 mb-1">Plan your trip</h4>
                <p className="text-gray-600 text-sm">Explore Makkah and plan your visit to the Holy Mosque. We&apos;ll send you local recommendations.</p>
              </div>
            </div>
            
            <div className="flex items-start">
              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mr-4">
                <span className="text-blue-600 font-semibold text-sm">3</span>
              </div>
              <div>
                <h4 className="font-medium text-gray-900 mb-1">Check in on {checkInDate.toLocaleDateString()}</h4>
                <p className="text-gray-600 text-sm">Arrive after 3:00 PM with your booking confirmation. The host will provide check-in instructions.</p>
              </div>
            </div>
          </div>
        </div>

        {/* Contact Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          <div className="bg-white rounded-xl shadow-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Property contact</h3>
            <div className="space-y-3">
              <div className="flex items-center">
                <Phone className="w-4 h-4 text-gray-500 mr-3" />
                <span className="text-sm text-gray-900">+966 12 234 5678</span>
              </div>
              <div className="flex items-center">
                <Mail className="w-4 h-4 text-gray-500 mr-3" />
                <span className="text-sm text-gray-900"><EMAIL></span>
              </div>
              <p className="text-xs text-gray-600 mt-3">
                Available 24/7 for any questions about your stay
              </p>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Customer support</h3>
            <div className="space-y-3">
              <div className="flex items-center">
                <Phone className="w-4 h-4 text-gray-500 mr-3" />
                <span className="text-sm text-gray-900">+966 11 123 4567</span>
              </div>
              <div className="flex items-center">
                <Mail className="w-4 h-4 text-gray-500 mr-3" />
                <span className="text-sm text-gray-900"><EMAIL></span>
              </div>
              <p className="text-xs text-gray-600 mt-3">
                For booking changes, cancellations, or assistance
              </p>
            </div>
          </div>
        </div>

        {/* Important Information */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-xl p-6 mb-8">
          <h3 className="text-lg font-semibold text-yellow-800 mb-3">Important information</h3>
          <ul className="space-y-2 text-sm text-yellow-700">
            <li>• Please bring a valid ID for check-in</li>
            <li>• Free cancellation until {new Date(checkInDate.getTime() - 48 * 60 * 60 * 1000).toLocaleDateString()}</li>
            <li>• Respect prayer times and local customs</li>
            <li>• Contact the property if you&apos;ll arrive after 11:00 PM</li>
          </ul>
        </div>

        {/* Navigation */}
        <div className="text-center space-y-4">
          <Link 
            href={`/${lng}`}
            className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
          >
            Back to home
          </Link>
          <div>
            <Link 
              href={`/${lng}/account`}
              className="text-blue-600 hover:text-blue-700 text-sm font-medium"
            >
              View all your bookings →
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
} 