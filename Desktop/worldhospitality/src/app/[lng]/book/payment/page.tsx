import { useTranslation } from '@/lib/i18n'
import { PaymentForm } from '@/components/booking/PaymentForm'
import { supabase } from '@/lib/supabaseClient'
import { transformRoomData, calculateBookingTotals } from '@/lib/room-utils'
import { BackButton } from '@/components/BackButton'

export default async function PaymentPage({ 
  params,
  searchParams 
}: { 
  params: Promise<{ lng: string }>
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>
}) {
  const { lng } = await params;
  const search = await searchParams;
  const { t } = await useTranslation(lng, 'common')
  const isRTL = lng === 'ar'

  // Extract booking parameters
  const roomId = search.roomId as string
  const checkin = search.checkin as string
  const checkout = search.checkout as string
  const guests = search.guests as string

  // Fetch the room from Supabase
  const { data: roomData, error } = await supabase
    .from('rooms')
    .select('*')
    .eq('id', parseInt(roomId))
    .single()

  if (error || !roomData || !checkin || !checkout) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Booking Not Found</h1>
          <p className="text-gray-600">Please start your booking process again.</p>
        </div>
      </div>
    )
  }

  // Transform the room data
  const room = transformRoomData(roomData)

  // Calculate booking details
  const checkInDate = new Date(checkin)
  const checkOutDate = new Date(checkout)
  const nights = Math.ceil((checkOutDate.getTime() - checkInDate.getTime()) / (1000 * 60 * 60 * 24))
  const bookingTotals = calculateBookingTotals(room.price, nights)

  return (
    <div className={`min-h-screen bg-gray-50 ${isRTL ? 'font-tajawal' : 'font-inter'}`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center mb-4">
            <BackButton />
            
            <h1 className="text-2xl font-bold text-gray-900">
              Complete your booking
            </h1>
          </div>
          
          {/* Progress indicators */}
          <div className="flex items-center space-x-4 text-sm">
            <div className="flex items-center text-green-600">
              <div className="w-6 h-6 bg-green-600 text-white rounded-full flex items-center justify-center text-xs mr-2">✓</div>
              Account
            </div>
            <div className="w-8 h-px bg-gray-300"></div>
            <div className="flex items-center text-blue-600 font-semibold">
              <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs mr-2">2</div>
              Payment
            </div>
            <div className="w-8 h-px bg-gray-300"></div>
            <div className="flex items-center text-gray-400">
              <div className="w-6 h-6 bg-gray-300 text-gray-600 rounded-full flex items-center justify-center text-xs mr-2">3</div>
              Review
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Payment Form */}
          <div className="lg:col-span-1">
            <PaymentForm 
              lng={lng}
              isRTL={isRTL}
              bookingParams={{
                roomId,
                checkin,
                checkout,
                guests
              }}
            />
          </div>

          {/* Booking Summary */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-xl shadow-lg p-6 sticky top-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-6">Booking Summary</h2>
              
              {/* Room Details */}
              <div className="flex gap-4 mb-6 pb-6 border-b border-gray-200">
                <div className="w-20 h-20 bg-gray-200 rounded-lg overflow-hidden flex-shrink-0">
                  <img 
                    src={room.image} 
                    alt={room.name}
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold text-gray-900 mb-1">{room.name}</h3>
                  <p className="text-sm text-gray-600">Near Holy Mosque, Makkah</p>
                  <div className="flex items-center mt-1">
                    <div className="flex items-center">
                      {[...Array(5)].map((_, i) => (
                        <svg key={i} className="w-3 h-3 text-yellow-400 fill-current" viewBox="0 0 20 20">
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                      ))}
                      <span className="text-xs text-gray-600 ml-1">4.9 (127)</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Trip Details */}
              <div className="space-y-4 mb-6 pb-6 border-b border-gray-200">
                <div>
                  <h4 className="font-semibold text-gray-900 mb-3">Trip details</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Dates</span>
                      <span className="text-gray-900">
                        {checkInDate.toLocaleDateString()} - {checkOutDate.toLocaleDateString()}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Guests</span>
                      <span className="text-gray-900">{guests || 1} guest{parseInt(guests || '1') > 1 ? 's' : ''}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Nights</span>
                      <span className="text-gray-900">{nights} night{nights > 1 ? 's' : ''}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Price Breakdown */}
              <div className="space-y-3 mb-6">
                <h4 className="font-semibold text-gray-900">Price breakdown</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">${room.price} × {bookingTotals.nights} nights</span>
                    <span className="text-gray-900">${bookingTotals.subtotal}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Service fee</span>
                    <span className="text-gray-900">${bookingTotals.serviceFee}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Taxes</span>
                    <span className="text-gray-900">${bookingTotals.taxes}</span>
                  </div>
                </div>
              </div>

              {/* Total */}
              <div className="pt-4 border-t border-gray-200">
                <div className="flex justify-between items-center">
                  <span className="text-lg font-semibold text-gray-900">Total (USD)</span>
                  <span className="text-2xl font-bold text-gray-900">${bookingTotals.total}</span>
                </div>
              </div>

              {/* Cancellation Policy */}
              <div className="mt-6 pt-6 border-t border-gray-200">
                <h4 className="font-semibold text-gray-900 mb-2">Cancellation policy</h4>
                <p className="text-sm text-gray-600">
                  Free cancellation for 48 hours. Cancel before {new Date(checkInDate.getTime() - 48 * 60 * 60 * 1000).toLocaleDateString()} for a full refund.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
} 