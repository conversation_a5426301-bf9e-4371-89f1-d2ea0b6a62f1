import type { Metada<PERSON> } from "next";
import { Inter, <PERSON><PERSON><PERSON> } from "next/font/google";
import "./globals.css";
import { languages } from '../i18n/settings'
import { ClientWrapper } from '../components/ClientWrapper'

const inter = Inter({ 
  subsets: ["latin"],
  variable: '--font-inter'
});

const tajawal = Tajawal({ 
  subsets: ["arabic", "latin"],
  weight: ['200', '300', '400', '500', '700', '800', '900'],
  variable: '--font-tajawal'
});

export const metadata: Metadata = {
  title: "World Hospitality",
  description: "Your one-stop destination for hotel bookings.",
};

export async function generateStaticParams() {
  return languages.map((lng) => ({ lng }))
}

export default async function RootLayout({
  children,
  params
}: Readonly<{
  children: React.ReactNode;
  params: Promise<{
    lng: string;
  }>
}>) {
  const { lng } = await params;
  const direction = lng === 'ar' ? 'rtl' : 'ltr';
  const fontClass = lng === 'ar' ? tajawal.variable : inter.variable;
  return (
    <html lang={lng} dir={direction} className={`text-blue-900 ${fontClass}`}>
      <body className={lng === 'ar' ? 'font-tajawal' : 'font-inter'} suppressHydrationWarning>
        <ClientWrapper>
          {children}
        </ClientWrapper>
      </body>
    </html>
  );
}
