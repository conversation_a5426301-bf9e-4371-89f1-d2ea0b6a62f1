-- Check Database Columns Script
-- Run this to verify all required columns exist in the rooms table

SELECT 
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'rooms' 
AND table_schema = 'public'
ORDER BY ordinal_position;

-- Expected columns for enhanced room form:
-- id, name, internal_name, description, image, images, price, price_currency, price_usd
-- amenities, capacity, rating, review_count, room_type, distance_to_haram, size
-- bed_type, bed_count, max_guests, bathroom_count, has_balcony, has_kitchen, has_bathroom
-- created_at, updated_at

-- If any columns are missing, run the migration script again 