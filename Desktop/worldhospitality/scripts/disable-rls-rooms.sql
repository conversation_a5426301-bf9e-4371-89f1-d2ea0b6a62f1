-- Temporarily disable <PERSON><PERSON> for rooms table testing
-- Run this in Supabase SQL Editor to allow room creation during development

-- Disable <PERSON><PERSON> on rooms table
ALTER TABLE rooms DISABLE ROW LEVEL SECURITY;

-- Drop existing restrictive policies
DROP POLICY IF EXISTS "Public rooms are viewable by everyone" ON rooms;
DROP POLICY IF EXISTS "<PERSON><PERSON> can insert rooms" ON rooms;
DROP POLICY IF EXISTS "Ad<PERSON> can update rooms" ON rooms;
DROP POLICY IF EXISTS "Ad<PERSON> can delete rooms" ON rooms;

-- Create permissive policies for development
CREATE POLICY "Allow all operations on rooms during development" ON rooms
  FOR ALL USING (true) WITH CHECK (true);

-- Also disable <PERSON><PERSON> on storage for room images
UPDATE storage.buckets 
SET public = true 
WHERE id = 'rooms';

-- Drop restrictive storage policies
DROP POLICY IF EXISTS "Public room images" ON storage.objects;
DROP POLICY IF EXISTS "Admins can upload room images" ON storage.objects;
DROP POLICY IF EXISTS "<PERSON><PERSON> can update room images" ON storage.objects;
DROP POLICY IF EXISTS "Ad<PERSON> can delete room images" ON storage.objects;

-- Create permissive storage policies
CREATE POLICY "Allow all storage operations during development" ON storage.objects
  FOR ALL USING (bucket_id = 'rooms') WITH CHECK (bucket_id = 'rooms');

-- Verify the changes
SELECT 
  tablename, 
  rowsecurity 
FROM pg_tables 
WHERE tablename = 'rooms';

-- Note: Remember to re-enable RLS after authentication is implemented! 