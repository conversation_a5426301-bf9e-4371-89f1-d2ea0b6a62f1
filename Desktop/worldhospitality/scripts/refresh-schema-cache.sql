-- Refresh Supabase Schema Cache and Check Database Structure
-- Run this in Supabase SQL Editor

-- 1. Force refresh the schema cache by notifying PostgREST
NOTIFY pgrst, 'reload schema';

-- 2. Check current rooms table structure
SELECT 
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'rooms' 
AND table_schema = 'public'
ORDER BY ordinal_position;

-- 3. Check if all our expected columns exist
SELECT 
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'rooms' AND column_name = 'capacity') 
        THEN 'EXISTS' ELSE 'MISSING' 
    END as capacity_column,
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'rooms' AND column_name = 'internal_name') 
        THEN 'EXISTS' ELSE 'MISSING' 
    END as internal_name_column,
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'rooms' AND column_name = 'price_currency') 
        THEN 'EXISTS' ELSE 'MISSING' 
    END as price_currency_column,
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'rooms' AND column_name = 'bed_count') 
        THEN 'EXISTS' ELSE 'MISSING' 
    END as bed_count_column,
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'rooms' AND column_name = 'max_guests') 
        THEN 'EXISTS' ELSE 'MISSING' 
    END as max_guests_column,
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'rooms' AND column_name = 'bathroom_count') 
        THEN 'EXISTS' ELSE 'MISSING' 
    END as bathroom_count_column;

-- 4. Show current table constraints
SELECT 
    constraint_name,
    constraint_type
FROM information_schema.table_constraints 
WHERE table_name = 'rooms' 
AND table_schema = 'public';

-- 5. Test a simple insert to verify the schema works
-- (This will be rolled back, just for testing)
BEGIN;
INSERT INTO rooms (
    name, 
    description, 
    image, 
    price,
    capacity
) VALUES (
    'Test Room', 
    'Test Description', 
    'test.jpg', 
    100,
    2
);
ROLLBACK;

-- If the above fails, it will show us exactly which columns are problematic 