-- Temporarily disable <PERSON><PERSON> for development
-- Run this in Supabase SQL Editor to allow room creation during testing

-- Disable RLS on rooms table
ALTER TABLE rooms DISABLE ROW LEVEL SECURITY;

-- Drop existing policies to avoid conflicts
DROP POLICY IF EXISTS "Public rooms are viewable by everyone" ON rooms;
DROP POLICY IF EXISTS "<PERSON><PERSON> can insert rooms" ON rooms;
DROP POLICY IF EXISTS "Ad<PERSON> can update rooms" ON rooms;
DROP POLICY IF EXISTS "Admins can delete rooms" ON rooms;

-- Optional: Also disable RLS on storage for room images
-- This allows image uploads without authentication
UPDATE storage.buckets 
SET public = true 
WHERE id = 'rooms';

-- Drop storage policies that might block uploads
DROP POLICY IF EXISTS "Public room images" ON storage.objects;
DROP POLICY IF EXISTS "Admins can upload room images" ON storage.objects;
DROP POLICY IF EXISTS "Ad<PERSON> can update room images" ON storage.objects;
DROP POLICY IF EXISTS "Ad<PERSON> can delete room images" ON storage.objects;

-- Create temporary permissive policies for development
CREATE POLICY "Allow all operations on rooms during development" ON rooms
  FOR ALL USING (true) WITH CHECK (true);

CREATE POLICY "Allow all storage operations during development" ON storage.objects
  FOR ALL USING (bucket_id = 'rooms') WITH CHECK (bucket_id = 'rooms');

-- Note: Remember to re-enable RLS after authentication is implemented! 