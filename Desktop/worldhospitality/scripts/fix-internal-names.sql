-- Fix Internal Names for Existing Rooms
-- Run this in Supabase SQL Editor

-- First, check current internal names
SELECT id, name, internal_name FROM rooms ORDER BY id;

-- Update existing rooms to have better internal names (only if they're currently ROOM-{id})
UPDATE rooms 
SET internal_name = CASE 
    WHEN internal_name LIKE 'ROOM-%' OR internal_name IS NULL THEN
        CONCAT('RM-', LPAD(id::text, 3, '0'), '-', UPPER(SUBSTRING(REPLACE(name, ' ', ''), 1, 3)))
    ELSE 
        internal_name  -- Keep existing custom internal names
END;

-- Force refresh schema cache
NOTIFY pgrst, 'reload schema';

-- Verify the update
SELECT id, name, internal_name FROM rooms ORDER BY id; 