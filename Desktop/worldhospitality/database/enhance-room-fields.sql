-- Enhanced Room Fields Migration
-- Add currency, bathroom count, bed count, max guests, internal name, and multiple images support

-- Add new fields to rooms table
ALTER TABLE rooms ADD COLUMN IF NOT EXISTS internal_name TEXT; -- Staff-only internal identifier
ALTER TABLE rooms ADD COLUMN IF NOT EXISTS price_currency TEXT DEFAULT 'SAR';
ALTER TABLE rooms ADD COLUMN IF NOT EXISTS price_usd INTEGER; -- Auto-calculated USD price
ALTER TABLE rooms ADD COLUMN IF NOT EXISTS bathroom_count INTEGER DEFAULT 1;
ALTER TABLE rooms ADD COLUMN IF NOT EXISTS bed_count INTEGER DEFAULT 1; -- Number of beds
ALTER TABLE rooms ADD COLUMN IF NOT EXISTS max_guests INTEGER DEFAULT 2; -- Maximum guests
ALTER TABLE rooms ADD COLUMN IF NOT EXISTS images TEXT[] DEFAULT '{}'; -- Array of image URLs

-- Add constraint for currency
ALTER TABLE rooms ADD CONSTRAINT check_currency 
  CHECK (price_currency IN ('SAR', 'USD', 'EUR', 'GBP'));

-- Add constraints for bed and guest counts
ALTER TABLE rooms ADD CONSTRAINT check_bed_count 
  CHECK (bed_count >= 1 AND bed_count <= 10);
  
ALTER TABLE rooms ADD CONSTRAINT check_max_guests 
  CHECK (max_guests >= 1 AND max_guests <= 20);

-- Update existing rooms with default values
UPDATE rooms SET 
  internal_name = CASE 
    WHEN name IS NOT NULL THEN CONCAT('RM-', id, '-', UPPER(SUBSTRING(name, 1, 3)))
    ELSE CONCAT('ROOM-', id)
  END,
  price_currency = 'SAR',
  bathroom_count = 1,
  bed_count = 1,
  max_guests = 2,
  images = CASE 
    WHEN image IS NOT NULL AND image != '' THEN ARRAY[image]
    ELSE '{}'
  END
WHERE price_currency IS NULL;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_rooms_currency ON rooms(price_currency);
CREATE INDEX IF NOT EXISTS idx_rooms_bed_count ON rooms(bed_count);
CREATE INDEX IF NOT EXISTS idx_rooms_max_guests ON rooms(max_guests);
CREATE INDEX IF NOT EXISTS idx_rooms_internal_name ON rooms(internal_name); -- For staff searches 