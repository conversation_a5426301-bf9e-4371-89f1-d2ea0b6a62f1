-- World Hospitality Database Schema
-- This file contains all the SQL commands to set up the database structure

-- ===============================================
-- ROOMS TABLE - Enhanced with new filtering fields
-- ===============================================

-- Drop existing table if you need to recreate (CAREFUL - this deletes data!)
-- DROP TABLE IF EXISTS rooms CASCADE;

-- Create or update rooms table with all new fields
CREATE TABLE IF NOT EXISTS rooms (
  id SERIAL PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT NOT NULL,
  image TEXT NOT NULL,
  price INTEGER NOT NULL,
  amenities TEXT[] NOT NULL DEFAULT '{}',
  capacity INTEGER NOT NULL DEFAULT 2,
  
  -- NEW ENHANCED FIELDS for advanced filtering
  rating DECIMAL(2,1) NOT NULL DEFAULT 4.0,
  review_count INTEGER NOT NULL DEFAULT 0,
  room_type TEXT NOT NULL DEFAULT 'standard' CHECK (room_type IN ('standard', 'deluxe', 'suite', 'family')),
  distance_to_haram INTEGER NOT NULL DEFAULT 500, -- in meters
  size INTEGER NOT NULL DEFAULT 25, -- in square meters
  bed_type TEXT NOT NULL DEFAULT 'Double',
  has_balcony BOOLEAN NOT NULL DEFAULT false,
  has_kitchen BOOLEAN NOT NULL DEFAULT false,
  has_bathroom BOOLEAN NOT NULL DEFAULT true,
  
  -- Metadata
  created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW())
);

-- Add update trigger for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = TIMEZONE('utc', NOW());
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_rooms_updated_at BEFORE UPDATE ON rooms 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ===============================================
-- BOOKINGS TABLE - Enhanced with proper relationships
-- ===============================================

CREATE TABLE IF NOT EXISTS bookings (
  id SERIAL PRIMARY KEY,
  room_id INTEGER NOT NULL REFERENCES rooms(id) ON DELETE CASCADE,
  
  -- Guest information
  guest_name TEXT NOT NULL,
  guest_email TEXT NOT NULL,
  guest_phone TEXT,
  num_guests INTEGER NOT NULL DEFAULT 1,
  
  -- Booking details
  check_in_date DATE NOT NULL,
  check_out_date DATE NOT NULL,
  total_price INTEGER NOT NULL, -- in cents
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'cancelled', 'checked_in', 'checked_out')),
  
  -- Payment information
  payment_status TEXT DEFAULT 'pending' CHECK (payment_status IN ('pending', 'paid', 'failed', 'refunded')),
  payment_intent_id TEXT, -- for Stripe integration later
  
  -- Special requests
  special_requests TEXT,
  
  -- Metadata
  created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW())
);

-- Add update trigger for bookings
CREATE TRIGGER update_bookings_updated_at BEFORE UPDATE ON bookings 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Add indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_bookings_room_id ON bookings(room_id);
CREATE INDEX IF NOT EXISTS idx_bookings_dates ON bookings(check_in_date, check_out_date);
CREATE INDEX IF NOT EXISTS idx_bookings_status ON bookings(status);
CREATE INDEX IF NOT EXISTS idx_rooms_price ON rooms(price);
CREATE INDEX IF NOT EXISTS idx_rooms_rating ON rooms(rating);
CREATE INDEX IF NOT EXISTS idx_rooms_room_type ON rooms(room_type);
CREATE INDEX IF NOT EXISTS idx_rooms_distance ON rooms(distance_to_haram);

-- ===============================================
-- USERS TABLE - For authentication (future)
-- ===============================================

CREATE TABLE IF NOT EXISTS users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email TEXT UNIQUE NOT NULL,
  name TEXT NOT NULL,
  phone TEXT,
  
  -- User preferences
  preferred_language TEXT DEFAULT 'en' CHECK (preferred_language IN ('en', 'ar')),
  
  -- Metadata
  created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW())
);

-- Add update trigger for users
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ===============================================
-- ROW LEVEL SECURITY (RLS) POLICIES
-- ===============================================

-- Enable RLS on all tables
ALTER TABLE rooms ENABLE ROW LEVEL SECURITY;
ALTER TABLE bookings ENABLE ROW LEVEL SECURITY;
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- Rooms policies - Public read access, admin write access
CREATE POLICY "Public rooms are viewable by everyone" ON rooms
  FOR SELECT USING (true);

CREATE POLICY "Admins can insert rooms" ON rooms
  FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Admins can update rooms" ON rooms
  FOR UPDATE USING (auth.role() = 'authenticated');

CREATE POLICY "Admins can delete rooms" ON rooms
  FOR DELETE USING (auth.role() = 'authenticated');

-- Bookings policies - Users can see their own bookings, admins see all
CREATE POLICY "Users can view their own bookings" ON bookings
  FOR SELECT USING (
    auth.uid()::text = guest_email OR 
    auth.role() = 'authenticated'
  );

CREATE POLICY "Users can create bookings" ON bookings
  FOR INSERT WITH CHECK (true);

CREATE POLICY "Admins can update bookings" ON bookings
  FOR UPDATE USING (auth.role() = 'authenticated');

-- Users policies
CREATE POLICY "Users can view their own profile" ON users
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile" ON users
  FOR UPDATE USING (auth.uid() = id);

-- ===============================================
-- STORAGE POLICIES (for room images)
-- ===============================================

-- Ensure the 'rooms' bucket exists
INSERT INTO storage.buckets (id, name, public) 
VALUES ('rooms', 'rooms', true) 
ON CONFLICT (id) DO NOTHING;

-- Storage policies for room images
CREATE POLICY "Public room images" ON storage.objects
  FOR SELECT USING (bucket_id = 'rooms');

CREATE POLICY "Admins can upload room images" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'rooms' AND 
    auth.role() = 'authenticated'
  );

CREATE POLICY "Admins can update room images" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'rooms' AND 
    auth.role() = 'authenticated'
  );

CREATE POLICY "Admins can delete room images" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'rooms' AND 
    auth.role() = 'authenticated'
  );

-- ===============================================
-- HELPFUL FUNCTIONS
-- ===============================================

-- Function to check room availability
CREATE OR REPLACE FUNCTION check_room_availability(
  room_id_param INTEGER,
  check_in_param DATE,
  check_out_param DATE
)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN NOT EXISTS (
    SELECT 1 FROM bookings 
    WHERE room_id = room_id_param 
    AND status IN ('confirmed', 'checked_in')
    AND (
      (check_in_param BETWEEN check_in_date AND check_out_date - INTERVAL '1 day') OR
      (check_out_param - INTERVAL '1 day' BETWEEN check_in_date AND check_out_date - INTERVAL '1 day') OR
      (check_in_date BETWEEN check_in_param AND check_out_param - INTERVAL '1 day')
    )
  );
END;
$$ LANGUAGE plpgsql;

-- Function to get available rooms for date range
CREATE OR REPLACE FUNCTION get_available_rooms(
  check_in_param DATE,
  check_out_param DATE,
  min_capacity_param INTEGER DEFAULT 1
)
RETURNS SETOF rooms AS $$
BEGIN
  RETURN QUERY
  SELECT r.* FROM rooms r
  WHERE r.capacity >= min_capacity_param
  AND check_room_availability(r.id, check_in_param, check_out_param);
END;
$$ LANGUAGE plpgsql; 