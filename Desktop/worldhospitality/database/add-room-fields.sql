-- Migration: Add new fields to existing rooms table
-- Run this in your Supabase SQL editor

-- Add new columns to existing rooms table
ALTER TABLE rooms ADD COLUMN IF NOT EXISTS rating DECIMAL(2,1) DEFAULT 4.0;
ALTER TABLE rooms ADD COLUMN IF NOT EXISTS review_count INTEGER DEFAULT 0;
ALTER TABLE rooms ADD COLUMN IF NOT EXISTS room_type TEXT DEFAULT 'standard';
ALTER TABLE rooms ADD COLUMN IF NOT EXISTS distance_to_haram INTEGER DEFAULT 500;
ALTER TABLE rooms ADD COLUMN IF NOT EXISTS size INTEGER DEFAULT 25;
ALTER TABLE rooms ADD COLUMN IF NOT EXISTS bed_type TEXT DEFAULT 'Double';
ALTER TABLE rooms ADD COLUMN IF NOT EXISTS has_balcony BOOLEAN DEFAULT false;
ALTER TABLE rooms ADD COLUMN IF NOT EXISTS has_kitchen BOOLEAN DEFAULT false;
ALTER TABLE rooms ADD COLUMN IF NOT EXISTS has_bathroom BOOLEAN DEFAULT true;

-- Add constraint for room_type
ALTER TABLE rooms ADD CONSTRAINT check_room_type 
  CHECK (room_type IN ('standard', 'deluxe', 'suite', 'family'));

-- Add indexes for better search performance
CREATE INDEX IF NOT EXISTS idx_rooms_price ON rooms(price);
CREATE INDEX IF NOT EXISTS idx_rooms_rating ON rooms(rating);
CREATE INDEX IF NOT EXISTS idx_rooms_room_type ON rooms(room_type);
CREATE INDEX IF NOT EXISTS idx_rooms_distance ON rooms(distance_to_haram);

-- Update any existing rooms with default values if needed
UPDATE rooms SET 
  rating = 4.0,
  review_count = 0,
  room_type = 'standard',
  distance_to_haram = 500,
  size = 25,
  bed_type = 'Double',
  has_balcony = false,
  has_kitchen = false,
  has_bathroom = true
WHERE rating IS NULL; 